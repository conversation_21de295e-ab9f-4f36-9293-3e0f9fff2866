const _extends = Object.assign || function (b) { for (let a = 1; a < arguments.length; a++) { const c = arguments[a]; var e; for (e in c)Object.prototype.hasOwnProperty.call(c, e) && (b[e] = c[e]); } return b; }; const _createClass = (function () { function b(a, b) { for (let c = 0; c < b.length; c++) { const d = b[c]; d.enumerable = d.enumerable || !1; d.configurable = !0; 'value' in d && (d.writable = !0); Object.defineProperty(a, d.key, d); } } return function (a, c, e) { c && b(a.prototype, c); e && b(a, e); return a; }; }()); let _class; let _temp;
function _defineProperty(b, a, c) {
  a in b ? Object.defineProperty(b, a, {
    value: c, enumerable: !0, configurable: !0, writable: !0,
  }) : b[a] = c; return b;
} function _classCallCheck(b, a) { if (!(b instanceof a)) throw new TypeError('Cannot call a class as a function'); } function _possibleConstructorReturn(b, a) { if (!b) throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); return !a || typeof a !== 'object' && typeof a !== 'function' ? b : a; }
function _inherits(b, a) {
  if (typeof a !== 'function' && a !== null) throw new TypeError(`Super expression must either be null or a function, not ${typeof a}`); b.prototype = Object.create(a && a.prototype, {
    constructor: {
      value: b, enumerable: !1, writable: !0, configurable: !0,
    },
  }); a && (Object.setPrototypeOf ? Object.setPrototypeOf(b, a) : b.__proto__ = a);
}
const _React = React; const { Component } = _React; const { PropTypes } = _React; const _ReactDOM = ReactDOM; const { render } = _ReactDOM; const files = './images'; const content = {
  title: 'RESTful API',
  subtitle: 'Gaman Base',
  btntitle: '',
  iphone: {
    title: 'Diseño', paragraph: 'Te ayudamos con el diseño de tu sitio web, landing page y aplicaciones móviles.', icons: ['adobe', 'sketch', 'svg'], images: { main: `${files}/iphone.png`, secondary: `${files}/headphones.png`, screens: [`${files}/iphone_base.svg`, `${files}/iphone_outlines.svg`, `${files}/iphone_content.svg`, `${files}/iphone_interative.svg`] },
  },
  ipad: {
    title: 'Web', paragraph: 'Nuestros desarrolladores web se dedican a la ingeniería de la más alta calidad.', icons: 'html css svg sass javascript json babel webpack react redux relay apollo'.split(' '), images: { main: `${files}/ipad.png`, screens: [`${files}/ipad_base.svg`, `${files}/ipad_outlines.svg`, `${files}/ipad_content.svg`, `${files}/ipad_interative.svg`] },
  },
  macbook: {
    title: 'Desarrollo',
    paragraph: 'Desde el desarrollo de aplicaciones empresariales hasta sitios web basados ​​en CMS.',
    icons: 'ubuntu nginx node socket graphql mongo jwt'.split(' '),
    images: {
      main: `${files
      }/macbook.png`,
      screens: [`${files}/macbook_base.svg`, `${files}/macbook_outlines.svg`, `${files}/macbook_content.svg`, `${files}/macbook_interative.svg`],
    },
  },
  books: { images: { main: `${files}/books.png` } },
  logo: { images: { main: `${files}/logo.png` } },
}; const textDefault = 'white'; const textMid = '#ABB3C0'; const textDark = '#717f96'; const backgroundLight = '#dde0e4'; const backgroundDark = textDark; const fontFamily = 'Montserrat, Helvetica, Arial, sans-serif'; const styles = {
  headline: {
    fontFamily, fontSize: '3vh', fontWeight: '300', marginBottom: '1vh', letterSpacing: '0.5vh', textTransform: 'uppercase',
  },
  text: {
    fontFamily, fontSize: '1.5vh', marginBottom: '2vh', fontWeight: '300',
  },
  full: { width: '100%', height: '100%' },
  center: {
    position: 'absolute', left: '50%', top: '50%', transform: 'translate3d(-50%, -50%, 0)',
  },
  flex: {
    display: 'flex', boxSizing: 'border-box', flexFlow: 'column nowrap', justifyContent: 'flex-start', alignItems: 'stretch',
  },
  colors: {
    textDefault, textMid, textDark, backgroundLight, backgroundDark,
  },
}; const Icon = (_temp = _class = (function (b) {
  function a() {
    _classCallCheck(this,
      a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments));
  }_inherits(a, b); _createClass(a, [{ key: 'render', value() { return React.createElement('svg', { viewBox: '0 0 540 540', xmlns: 'http://www.w3.org/2000/svg', ...this.props }, React.createElement('g', { fill: 'currentColor', stroke: 'currentColor' }, this.list(this.props.name))); } }, {
    key: 'list',
    value(a) {
      switch (a) {
        case 'adobe': return React.createElement('g', null, React.createElement('title', null,
          'Adobe'), React.createElement('path', { d: 'M322.3 92.5l148.3 355v-355H322.3zm-252.9 0v355l148.4-355H69.4zm135.8 283.6h69.2l28.3 71.4h61.9l-94.5-224.2-64.9 152.8z' })); case 'apollo': return React.createElement('g', null, React.createElement('title', null, 'Apollo'), React.createElement('path', { d: 'M309.6 111h-77.8L119.6 402.3h70.3l18.3-49.3h106.1l-19.2-54.6h-69.8L270.7 173l80.8 229.3h70.3z' }), React.createElement('path', { d: 'M469.4 423.2c-3.8 0-7.2 1.9-9.3 4.8 0 0-10 11.4-15.3 16.7-22.7 22.7-49.1 40.5-78.6 53-30.5 12.9-62.8 19.4-96.2 19.4-33.4 0-65.7-6.5-96.2-19.4-29.4-12.5-55.9-30.3-78.6-53-22.7-22.7-40.5-49.1-53-78.6-12.9-30.5-19.4-62.8-19.4-96.2s6.5-65.7 19.4-96.2c12.5-29.4 30.3-55.9 53-78.6s49.1-40.5 78.6-53c30.5-12.9 62.8-19.4 96.2-19.4 33.4 0 65.7 6.5 96.2 19.4 21.4 9.1 41.3 21 59.3 35.6-1 2.8-1.5 5.9-1.5 9 0 15.4 12.5 27.9 27.9 27.9 15.4 0 27.9-12.5 27.9-27.9 0-15.4-12.5-27.9-27.9-27.9-3.8 0-7.5.8-10.8 2.2C394.5 22.9 334.9 0 270 0 120.9 0 0 120.9 0 270s120.9 270 270 270c83.4 0 158-37.9 207.5-97.3 2.1-2.1 3.3-4.9 3.3-8.1 0-6.3-5.1-11.4-11.4-11.4z' }));
        case 'apple': return React.createElement('g', { id: 'apple' }, React.createElement('title', null, 'Apple'), React.createElement('path', { d: 'M474.9 386.9c-2.8 8.1-5.8 15.9-9 23.3-7.8 17.5-16.9 33.7-27.5 48.5-14.4 20.3-26.2 34.3-35.4 42-14.1 12.8-29.3 19.4-45.5 19.7-11.7 0-25.7-3.3-42.1-9.9-16.4-6.5-31.4-9.9-45.2-9.9-14.5 0-29.9 3.3-46.5 9.9-16.6 6.6-30 10-40.2 10.4-15.6.6-31.1-6.1-46.5-20.2-9.9-8.4-22.2-22.9-37-43.5-15.9-21.9-28.9-47.3-39.1-76.4-10.9-31.3-16.4-61.6-16.4-91 0-33.7 7.4-62.7 22.2-87 11.7-19.5 27.2-35 46.5-46.3 19.4-11.3 40.4-17.1 62.9-17.4 12.4 0 28.5 3.8 48.7 11.2 20.1 7.4 32.9 11.1 38.6 11.1 4.2 0 18.5-4.4 42.8-13.2 22.9-8.1 42.3-11.4 58.2-10.1 43 3.4 75.3 20 96.7 50.1-38.4 22.9-57.4 55-57.1 96 .4 32.1 12.2 58.7 35.4 79.9 10.7 9.9 22.4 17.5 35.5 22.8z' }),
          React.createElement('path', { d: 'M366.7 19c.4 3.4.5 6.7.5 10.1 0 25.1-9.3 48.5-27.9 70.2-22.5 25.8-49.6 40.7-79.1 38.4-.4-3-.6-6.2-.6-9.5 0-24.1 10.6-49.9 29.7-71 9.5-10.7 21.5-19.5 36.1-26.5 14.6-7.2 28.4-11 41.3-11.7z' })); case 'babel': return React.createElement('g', null, React.createElement('title', null, 'Babel'), React.createElement('path', { d: 'M487.2 173.6c-1.4 6.3-3.6 11-6.7 14.2l-2.2-1.4c2.3-6 3.5-9.5 3.4-10.3l-.6-1c-.8.1-1.8 2.2-3.1 6.3l-1.1.1c-.1-.8-.3-1.4-.6-2l-.5.1c-10.2 28.6-14.9 41.9-24.3 63.8-1.7 5.6-3.2 8.8-4.5 9.4.1.6.4 1 1.1.9.3 2.8-.4 5.5-1.9 8.2-4.4 12.6-7.3 19.5-8.8 20.8l1 6c.1.6-.3 1-1 1l-1 .1c-.2-1.7-.8-2.4-1.8-2.3l-.5.1c-1.3 2.1-2.3 4.5-3 7.3l1-.1c1.4-.9 2-1.9 1.9-3.2l2.1-.2 1.1.4c-1 1.5-1.4 2.7-1.3 3.6l.5 1-1.3 3.1.1.5c1.2-.1 2.5.3 3.9 1.1 1-.1 2-.5 3.1-1.2 1.2-.1 5.6-.2 13.2-.2l3.8-1.3c4.7-.1 5.4.1 9.5-.3l1.7-.2.9-.6.1.5c4.2-.3 4.5-.1 7.8.3l.1.5-2.3.2.1 1c.7-.1 1.3-.3 1.8-.7.6-.1 1 .3 1 1-1 .1-2 .3-3.2.7l-1-.3c-1.7.4-1.3.5-2.3.6l-1.7-.3c0 .3-.1.4-.3.5.1.6.3 1 1 .9l2-.2-.9-.3c0 .3.2.4.5.4l8.6-.8 3.3-.7c0 .3.2.4.4.4l1.6-.6 3-.3c2.3-.2 2.3-.3 7.2-1.1 0-.3.1-.4.3-.4v.4c9.5-1.6 8.6-1.5 10.6-2.2l-.4-1c-2.7.6-2.6.3-4 .4l-7.2.3-.1-.5 4-.5 1.5-.1.8-.6.1.5c5.6-.5 7.6 1 34-4.6 1.4-.1 1.3-1.3 2.2-3.7-.5 0-1-.1-1.4-.3-2.2.9-3.3.4-7.5 1.1-.3 0-.6-.3-.8-1v-.4l4 .2c.7-.1 2.3-.2 4.3-1.4.4.3-.3.5.1.4l.7-.1c.4 0 .6-.3.6-1l-.4-1c-2.7.3-4 .3-6.2 1.6l-.1-.5-3.3.3c-.1-1-.3-1.5-.8-1.5l-1.7.2c-2 .2-3 .1-3.9.9 0-.3-.2-.5-.3-.5l-1.9.2-1.1.6c0-.3-.2-.5-.3-.5l-.7.6-.1-.5-2.6-.2-.6.1.1 1 1.4.3 1.2-.1c.4.3 1 .3 1.5.3l1.1-.5v.4c-6.2 1.6-6.2 1.2-10.4 2-.5-.3-.9-.4-1-.4-.4.4.5.1.3.1-.7.1-1-.3-1.1-.9l2.2-.3c.2 0 .3.2 1.5.4 1.7-.9 1.8-1.2 2.8-1.3l-.1-1c-6.1 1.2-3.6.4-11.3 1h-.3l-.1-1c9.8-1.2 6.5-.5 9.8-1.5 0 .3.2.5.3.5.3-.3.9-.6 1.4-.6l.7-.1c3.4-.3 4.6.4 11.4-1.5v.4l1.1-.5 1-.1c-.1-1 1.5-1.1 1.9-1.2l-.1-1c-11.9 2.4-15 1.7-18.3 2.7v-.4c-3.1.6-3.2.3-4 .3-.3 0-.6.3-1 .6 0-.3-.2-.5-.4-.5-.5 0-.3.2-.7.6l.7-.6-10.3 1.7c0-.3-.2-.4-.4-.4-5.2 1.1-3.8 1.3-10.9 1.9-.3 0-.8.2-1.3.5 0-.3-.2-.4-.5-.4l-4.3.3-2.9.7c0-.3-.2-.4-.5-.4-1.7.4-3 .7-4.2.8h-.1c-.7.1.3-.2-.2-.4l-.9.5c-1.3.1-.6-.1-5 0-7.6 1-6.8 1-7.6 1.1l-2.9-.2c0 .3-.1.4-.4.5-3.2-.4-4.9-1-5-1.6.4-3 1.1-4.6 2.3-4.8l.5-.1-.5 1 .5-.1c1-.5 1.5-1 1.5-1.6l-.1-1-1 .1-.1-1c-.1-.8 0-1.4.3-2l2.3-3.7.1 1 1.1-.1-.1-1-.6-1c.8-.7 1.5-1.1 2-1.1l-.1-.5-1.5.1-.1-1.5c3-6.3 4.4-9.9 4.3-10.9l1 .4c.7-.1 1-.4 1-1.1l-1 .1v-.4c-.1-.8.1-1.5.3-2.1 1.7-1.7 2.3-3.4 2.2-5.2.3 0 .5.2.5.5 1.4-.8 2.4-2.9 3.2-6.3 2.4-4.4 5.9-13.3 10.4-26.5 8.7-23.4 8.3-25.9 9.6-31.4l-1 .1c.1.7-.1 1.4-.4 2-.7-.3-1.3-.4-1.7-.4v-.4c-.2-2 .7-4.4 2.6-7.3h.4l.9 3.4 1-.1c0-.3.3-1.6.8-4.1-.1-.5-.4-.8-1.2-.9l-1 .6c-.1-.7 0-2.5.1-5.6l-1.4.5zm-12.5 15l.5-.1c-.8 1.4-1.4 2.2-2 2.3l-.1-1.5c.5-.1 1-.3 1.6-.7zm-4.3 10.4c1-4.3 2.1-6.7 3-7.3l.1.5c-.5 4-1.4 6-2.6 6.2l.2 2.5c-1 1.3-1.4 2.3-1.3 3.1l-.5.1-.3-3.5c.5 0 1.1-.5 1.4-1.6zm-1.7 5.7c.1.6-.3 2-1.1 4.1l-.5.1-.1-1.5c.6-1.8 1.2-2.6 1.7-2.7zm-28.5 63.1l.5 1c.8-.1 1.1-.4 1-1l-.1-.5-.5.1-.9.4zm13.7 11.4v.4c.8-.1 1.6-1.5 2.3-4.2l-1-.3c-1 2.4-1.3 3.7-1.3 4.1zm49.8 7l.7.9c0-.3.1-.4.3-.4l.5 1-2.2-.3c-.1-.7.1-1.1.7-1.2zm-7.2.8c.8-.1 1.3.4 1.3 1.1h-2.4v-.4c.3-.1.7-.2 1.1-.7zm5.6-.2l.1.5-2 .2-.1-.5 2-.2zm-20.1 2c0 .3.2.4.5.4 3.2-.6 2.9-.7 4.4-.9.5-.1.8.3.9.9l-8.9.8.9.4-.1-.5-3.4.3-.1-.5 3-.3 2.8-.6zm9.2-.7v.4c-.3 0 .9.1.3.5-1.9-.2-2-.3-3.4-.3v-.4l3.1-.2zm2.2-.3v.4l-1.7.2v-.4l1.7-.2zm-55.6 8.9l.1 1.5c-.5.1-1-.3-1.1-1v-.4l1-.1zm74.4-1.3l.1 1 1.7-.2-.7-.4.1.5 1.7-.7c11.9-1.3 13.3-.8 13.1-2.2v-.4c-11.9 2.6-13.5 2.1-16 2.4zm18.6-1.7l1.7.3-.1-1-1.7-.3.1 1zm-51.5 3.4c.5.3-.6.5.1.4l6.1-.5.1.5c-1.7.2-2.9.4-3.2.8 0-.3-.2-.5-.5-.5l-2.6.3-.1-.5c.7-.2-.4-.2.1-.5zm28.2-.7l.5 1c2-.4.9-.3 1.4-.8 0 .3.2.5.3.5l1.4-.1-.1-1-3.6-.1.1.5zm-34.4 3.7h-.3l-.1-1h.3l.1 1zm-27.2 3.3l.1.5 16-1.3-.1-1-12.8 1-2.7-.3-.5 1.1zm17.8-.9h.2l-.1-1h-.2l.1 1z' }),
          React.createElement('path', { d: 'M220.5 218.6c-11.3 13.5-17.1 20.8-17.5 22.3-4.6 4.8-6.9 7.6-6.6 8.6-1.2.8-1.7 1.6-1.6 2.4-.3.1-.6-.1-.7-.4-2 3.2-4.9 5.2-8.8 6.2l-4.1 1c-.5.1-.8.5-.8 1.2l.1.5 1.6-.3.1.4-3.5 1.3-6.2 1.4-3.2.2c-.4.4 2.5-.1 1.4.3l-4 1c-2.4.6-3.8.3-4-.6l-.4.1c.3 1 0 1.6-.7 1.7l.8-.2-.3-1.5-7.3 1.7.8.3c-.1-.3-.4-.4-1.1-.3l-1 .8-1.1-.3c-1 .6-1.7 1-1.9 1l-1.1-.3c-3 1.7-.8 1.7-1 1.7l.3 1 1.7-.4 2.7-.2-.3 1.1c-9.9 2.6-2.7 1.1-5.4 1.7l-.5.1.3 1 1.6-.3.1.5c-2 .4-3.1.9-3.5 1.3l-.1-.5c-2.6.6-5.2 2.6-7.8 5.9.4 1.9-5.2 4.3 21.4-1.9 1.8-.1 2.9-.1 3.1-.2-.1-.3.1-.5.4-.6 3-.3 1.6.2 3.4-.3l5.7-1.3-1.2.7c-.1-.3.1-.4.3-.5.7.2 1.4.1 2.2-.1l.3 1.5-22.7 36-5 6.3-2.4 8.3.4-.1 2.4-1-.8 3.2.3 1 .7.9c-.3.1-.4.3-.3.6l.1.5 1-.3c1.5-1.3 2.1-2.5 1.9-3.6 1.7.3 2.7.3 3.2.3l.1.5c-1 .3-1.5 1-1.6 2.3l.1.5.5-.1c8.3-8.9 28.3-39.6 44.5-60.5-.5-2.3 7.9-5.4 25.3-9.4l.5-.1c.6 2.7-.7 9.3-4 19.8-2.2 6.3-3.1 9.8-3 10.3-2.8 8.4-4.1 12.9-3.9 13.6l-6.2 20.2c-4 10.3-7 21.2-9.1 32.7.3-.1 1 0 1.7.2l.9-.8c.1.3.3.4.7.4l-.3-1.5 2.7-.1c.8-.2 1.5-1.2 2-3-.1-2.7.4-4.2 1.7-4.5 1.7-6.4 2.7-9.7 3-9.8 1.6-6.4 2.7-10.1 3.5-11 2.3-8.3 4-12.7 5.1-12.9l.3 1-1.7 4.4c-3 13.1-6 23.5-9 31.2l.9 3.8.5-.1c7.4-15.8 17.9-47.1 31.7-93.8.4-4.1 2.3-7.3 5.9-9.6l-.8-.8-.1-.4c1.6-.3 2.7-1 3.3-1.8-.1-.3-1.5-.9-4.2-1.6l8.2-34.9c2.4-10.4 4.1-19.5 5-27.5l-.1-.5c-1.7-1-3.3-2.5-4.8-4.4v-2.1l-.1-.4-.4.1c-5.3 7.6-15.3 20.8-30.2 39.8zm11.6 7.9c.4 1.8-.3 5.2-2 10.2-.5 6.3-2.8 9.8-6.7 10.8l-.1-.5-3.5 1.3-1.6.3c-.8.2-1.5.2-2.2-.1l-.3-1.4c-.1-.5 4.9-7.5 15.1-20.8l1.3.2zm-8 21.7l1.1-.3.3 1c-5.5 1.7-8.5 2.5-9.1 2.7-.3.1-.9.3-1.5.9-.1-.3-.8-.3-2.2-.1-.3-1 3.6-2.4 11.4-4.2zm-54 14.6l1.2-.3-.3-1-1.2.3.3 1zm15.9-4.2l.3 1-2.6.2-.1-.5 2.4-.7zm-30.4 10l.1.5-1.7.3v-.4l1.6-.4zm-3 6.4l.1.4-2 .4-.3-1 2.2.2zm4.8-1.1l.1.4-3.6.9-.1-.4 3.6-.9zm6.3-1l.1.5-9.6 2.2-.3-1c5.6-1 5-.6 9.8-1.7zm47.9-11.2l.8.9c-1.1.3-2 .6-2.4 1l-1.1-.2c-1.7.7-3.3 1.2-4.5 1.5l-1 .3c-.6.2-1-.1-1.3-.7l7.2-1.7 2.3-1.1zm5.1-1.1l5.1-1.2.1.5c-2.8.6-5 1.3-6.5 2.1l-.1-.5c.4-.1.9-.4 1.4-.9zm-53.9 14.6l2.4-.5.3 1c-1 .3-.3.3-1.6.9-2.5.3-2-.1-6 .9l-.1-.5 5-1.8zm6.6-1.1c.7-.2 1.1.1 1.2.8l-1.5.3c-.6.2-1-.1-1.3-.8l1.6-.3zM142.2 329l.1.5c.7-.2 1-.5.8-1.2l-.1-.5c-.6.1-.8.5-.8 1.2zm2.1 8.7c.2.6.5.9 1.2.7.7-.2 1-.5.8-1.2l-.1-.5-1 .3-.9.7zm55.4 14.9c.7-.2 1 .1 1.2.7l.1.5-2 .4c.2-.7.2-1.2.1-1.6h.6z' }),
          React.createElement('path', { d: 'M280.2 191.9c-4.5 3.2-9.6 6.5-15.4 9.7l.1 1c.3 0 .9-.3 1.6-.7.7-.1 1 .3 1.1.9l1-.5h.4v.4c.1.5-3.6 3.3-10.7 8.3l.5 1h-.4l-1.1-.4c0 .3-.4.6-1.5.7v.4l1.1 1.4c-.3 0-1-.1-1.7-.4-2.3.2-4.6 1.9-6.9 5l.5 1c2-1.8 3.1-2.8 3.5-2.8l.1 1.5c-.3 0-.9.3-1.6.7l1.3 1.8c2.3-2.6 5-4.8 7.7-6.6 1.4.4 2.2.9 2.2 1.3l1-.1c7.8-5.7 15.5-9.8 23-12.3l.1 1c-1.4 2.1-2.3 3.1-2.9 3.2.1.7.3 1.3.6 1.9.1 1.3-3.3 10-10.2 26.1-15.9 37.3-29.2 64.9-40 82.8 0 .3.3.9.7 1.5 2.7-.6 4.3-1.3 5-1.9h.4l.1 1 1.1-.1 1-.5c0 .3.3.4 1 .3l.1 1c.1 1-.5 2.4-1.7 4.5-1.1 1.2-2.3 3.8-3.6 7.7l.1.5 1-.1c4.3-4.9 7.6-9.6 9.8-14.2 12.6-3.7 22.1-7.3 28.8-10.9 6.8-.6 11.8-2.3 15.3-5.2l-.1-.5-2.6.8-.5.1-.1-.5c5-.7 8.3-1.8 10.2-3.3 9.6-7.4 16.8-12.6 21.7-15.6 15.1-11.1 22.2-21.7 21.4-32-.1-1-3.4-5.2-10-12.5-.1-1.4 2.2-3.4 6.8-6l13.2-11.5c3-3.7 4.7-9.8 5.2-18.2l-.2-1.9c-.5-5.8-4.8-10.5-12.8-14.2-4.8-3.2-13.3-5-25.5-5.3-9.5.6-22.8 4.3-39.2 10.7zm70.7.7l-.5 1-14.6-6.2c9.8.5 14.8 2.2 15.1 5.2zm-59.3 40.7c1-5.5 2.2-8.5 3.4-9.2l11-24.7c-.1-1.5 2.2-2.7 6.9-3.6l1.6-.2.1 1.5c4.7-.8 7.6-1.2 8.8-1.3 8.7-.8 13.2 1 13.5 5.2l1-.1-.2-2.3 1-.1c2.5 1.4 3.8 3 4 5 .1 1.4-.6 3.1-2.1 5.2-.7.1-1-.4-1.1-1.4l-1 .1-.3 3c-4.4 6.7-7.6 10.1-9.6 10.2-1.7 2.4-2.9 3.6-3.4 3.6-1.4 1.7-5.1 4.5-11.3 8.4-2.1.2-9.6 3.1-22.8 8.9-.6-.3-1.4-.3-2.1-.3l-.1-1c.1-1.6.9-4 2.7-6.9zm60.7-30.4l.1 1c-.5 0-1.5-1.6-3-4.7l-.1-1c.9 0 1.9 1.5 3 4.7zM256 214.7l.1.5c-.3 0-.9.3-1.6.6h-.4l-.1-1 2-.1zm31.9-.8l.1 1.5c-.5 0-1.1.8-1.9 2.1l-.1-1c.9-1.1 1.3-2 1.3-2.6h.6zm-4.9 10.2l.2 1.9-.5.1-.2-1.9.5-.1zm-1.3 3.6c-.1 1.7-.5 2.5-1.3 2.6l-.5.1c.6-1.1.9-2 .8-2.6l1-.1zm-2.2 5.1l.1.5-.9 1.6-1 .1-.1-.5c1-.1 1.6-.6 1.5-1.7h.4zm-2.2 4.6l-.3 2.5h-.4l-.2-2.5h.9zm53.5 1.9l2.2 1.3c.1.6-.3 1-1 1-1-.6-1.7-.9-2.2-.8v-1.5h1zm-41.8 5.5l.1 1-2.1.2-.1-1 2.1-.2zm33.7 3.6c1.5.7 2.2 1.3 2.3 1.8v.4c-1.4.1-2.7-.6-3.9-2.2h1.6zm-43.5 10.7c5.9-.5 15.2-3.3 28-8.3l3.2-.3c6.4-.5 11.2.7 14.4 3.7l.2 1.9c-2.3 6.8-5 10.8-7.8 12l-13.1 10.6c-9.8 6.4-15.1 9.6-16 9.7-15.6 8.6-25.4 13-29.2 13.4l-.5.1c.3-1.8 7.3-16.1 20.8-42.8zm14.2-5.7l.1.5-2.1.2-.1-.5 2.1-.2zm47.3-2.2l.3 4c-.5 1-1 1.5-1.5 1.6l-.5-6.8c1.1.4 1.7.8 1.7 1.2zm-80.2 34.1c-.7 3.3-1.4 5-2.1 5v-.4c-.2-1.6.5-3.1 2.1-4.6zm13.2 18.6c3.8-.7 14.3-6.2 31.5-16.5l.1 1c0 .4-1.4 1.7-4.4 3.8-8.7 4.3-13.9 7.2-15.5 8.7-9.5 3.3-14.2 5.4-14.1 6.2-8.4 3.3-13.9 5.7-16.6 7.3-.6.1-1.6-.2-2.7-.8-.2-1.7.6-3 2.3-4.1 1.5-.1 2.9.1 4.2.6 1.6-.8 4.3-1.7 8.2-2.7l-.1-1-3.2.3c.3-.6 3.4-2 9-4.3l1.6-.2v.4c-2.7.3-4.2 1-4.5 2.4.1.6.4 1 1.1.9 2.1-1 3.1-1.8 3.1-2zM257.4 292v.4c.1.7-.3 1.1-1 1.1l-.1-.5c.1-.4.4-.9 1.1-1zm24.7 6.8c-1.2.1-3.6 1.1-7 3l-.5.1-.1-1c2.5-.3 4.8-1.2 6.5-3 .7 0 1 .2 1.1.9zm-25.5 7.1l1.6-.2.1.5c-.3 0-.9.3-1.6.6l-1 .1c-.2-.5.2-.9.9-1zm-10.6 5.8l1 .4c-.3 3-1.3 4.6-2.7 4.7-1.2-.5-2.3-.8-3.2-.7l-.1-1.5c-.1-.5.3-.9 1-1 .7-.1 1 .4 1.1 1.4 1.8-2.2 2.8-3.3 2.9-3.3z' }),
          React.createElement('path', { d: 'M75.7 187.6c-5.9 4.3-12.7 8.6-20.2 12.8l.1 1.3c.5 0 1.1-.3 2-.8.9-.1 1.4.3 1.4 1.1l1.4-.8.7-.1.1.6c.1.6-4.7 4.3-14.2 11l.8 1.2-.7.1-1.5-.5c0 .4-.6.7-2 .8l.1.7 1.6 1.7c-.5 0-1.2-.1-2.1-.4-3 .3-6.1 2.5-9 6.7l.8 1.2c2.6-2.3 4.1-3.6 4.5-3.6l.2 1.9c-.4 0-1.1.3-2 .8l1.7 2.4c3.1-3.4 6.5-6.3 10.3-8.6 1.9.5 2.9 1 2.9 1.7l1.4-.1c10.2-7.6 20.2-12.9 30.2-16.3l.1 1.3c-1.8 2.8-3.1 4.2-3.8 4.3.1 1 .4 1.7 1 2.5.2 1.7-4.3 13.1-13.5 34.3-21.3 49.3-38.8 85.6-53 109.2 0 .4.3 1.1.8 1.9 3.5-.9 5.7-1.7 6.7-2.5l.8-.1.1 1.3 1.3-.1 1.4-.8c0 .4.5.6 1.4.5l.1 1.3c.1 1.3-.6 3.3-2.3 6-1.5 1.7-3 5-4.6 10.2l.1.6 1.3-.1c5.6-6.3 10-12.6 12.9-18.7 16.6-4.9 29.2-9.6 38-14.3 8.8-.8 15.5-3 20.1-6.9l-.1-.7-3.3 1-.8.1-.1-.7c6.4-1 10.9-2.4 13.4-4.3 12.6-9.7 22.1-16.6 28.5-20.7 19.9-14.6 29.3-28.7 28.1-42-.1-1.4-4.5-6.9-13.1-16.4-.2-1.8 2.9-4.4 8.9-7.9l17.3-15.2c3.8-5 6.2-12.9 6.9-24l-.3-2.6c-.7-7.7-6.3-14-16.9-18.8-6.3-4.3-17.5-6.5-33.6-6.8-13.2 1.2-30.7 5.9-52.3 14.3zm93.1 1.1l-.5 1.3-19.3-8.1c12.9.5 19.5 2.8 19.8 6.8zm-78 53.5c1.3-7.1 2.8-11.2 4.4-12.2l14.4-32.4c-.2-2 2.9-3.6 9.2-4.6l2.1-.2.2 1.8c6.3-1 10.1-1.5 11.6-1.7 11.5-1 17.4 1.3 17.9 6.9l1.3-.1-.3-3.3 1.4-.1c3.4 1.8 5.2 4.1 5.4 6.8.2 1.8-.8 4.1-3 6.8-.9.1-1.4-.5-1.5-1.8l-1.4.1-.3 3.9c-5.8 8.7-10 13.1-12.6 13.4-2.3 3.2-3.8 4.9-4.4 5-1.8 2.2-6.8 5.9-14.9 11.1-2.7.3-12.7 4.1-30 11.6-.9-.4-1.7-.5-2.8-.4l-.1-1.2c-.1-2.4.9-5.6 3.4-9.4zm79.9-39.9l.1 1.2c-.7.1-2-2-3.9-6.2l-.1-1.2c1.2-.2 2.5 1.9 3.9 6.2zM43.8 217.7l.1.6c-.4 0-1.1.3-2 .8l-.7.1-.1-1.2 2.7-.3zm42-1l.2 1.9c-.6.1-1.5 1-2.5 2.8l-.1-1.3c1.2-1.5 1.8-2.6 1.8-3.4h.6zm-6.4 13.5l.3 2.6-.7.1-.3-2.6.7-.1zm-1.7 4.7c-.2 2.2-.7 3.3-1.8 3.4l-.7.1c.8-1.5 1.2-2.5 1.1-3.3l1.4-.2zm-2.9 6.8l.1.6-1.2 2.1-1.3.1-.1-.6c1.3-.1 1.9-.8 1.8-2.1l.7-.1zm-2.9 6.1l-.3 3.2-.7.1-.3-3.2 1.3-.1zm70.4 2.3l2.9 1.7c.1.9-.3 1.4-1.2 1.5-1.2-.8-2.2-1.1-2.9-1.1l-.2-1.9 1.4-.2zm-55.1 7.4l.1 1.2-2.7.3-.1-1.2 2.7-.3zm44.5 4.5c1.9.9 2.9 1.7 3 2.4l.1.6c-1.8.2-3.5-.8-5-2.9l1.9-.1zm-57.4 14.1c7.8-.7 20.2-4.3 36.9-10.9l4.1-.3c8.5-.7 14.9.9 19 4.9l.3 2.6c-3.1 9-6.6 14.2-10.3 15.9L106.9 302C94 310.5 87 314.7 86 314.8c-20.6 11.4-33.4 17.2-38.4 17.6l-.8.1c.7-2.4 9.8-21.2 27.5-56.4zm18.8-7.4l.1.6-2.8.3-.1-.6 2.8-.3zm62.4-2.8l.4 5.1c-.6 1.4-1.2 2.1-1.9 2.1l-.8-9c1.5.6 2.2 1.2 2.3 1.8zM49.7 310.7c-.9 4.4-1.8 6.7-2.8 6.8l-.1-.6c-.1-2.3.9-4.3 2.9-6.2zm17.4 24.5c5-1 18.8-8.3 41.5-21.7l.1 1.2c.1.5-1.9 2.2-5.8 5-11.6 5.6-18.3 9.6-20.3 11.6-12.5 4.3-18.7 7-18.6 8.1-11.1 4.4-18.4 7.6-21.9 9.6-.8.1-2-.3-3.6-1-.2-2.3.8-4.1 3-5.6 1.9-.2 3.8.1 5.6.9 2.1-1 5.6-2.3 10.8-3.5l-.1-1.3-4.1.3c.5-.7 4.4-2.5 11.9-5.6l2.1-.2.1.6c-3.5.3-5.5 1.4-5.9 3.2.1.8.5 1.2 1.4 1.1 2.5-1.5 3.8-2.4 3.8-2.7zm-21.4-15.7l.1.6c.1.9-.3 1.4-1.2 1.5l-.1-.6c-.1-.8.4-1.3 1.2-1.5zm32.5 8.9c-1.7.2-4.7 1.5-9.3 4.1l-.7.1-.1-1.3c3.3-.3 6.2-1.6 8.5-3.9 1-.2 1.5.2 1.6 1zm-33.7 9.3l2.1-.2.1.7c-.5 0-1.1.3-2 .8l-1.3.1c-.1-.6.3-1.1 1.1-1.4zm-13.8 7.8l1.5.6c-.5 3.9-1.7 5.9-3.6 6.1-1.6-.7-3-1-4.3-.9l-.2-1.9c-.1-.8.3-1.2 1.3-1.5.9-.1 1.4.5 1.5 1.8 2.3-2.8 3.6-4.2 3.8-4.2z' }),
          React.createElement('path', { d: 'M416 174.8l-1.1-.4-18.5 2.9c-.7-.3-1.6-.5-2.5-.4-1 .1-2.6.4-5 1l-8.9 3.7.7 1.1c.4 0 1-.3 1.8-.7l3.1.3-.4 3c-2.9 1.3-4.6 3.2-5 5.7-4.3 1.9-6.5 3.3-6.5 4.2l.1.5.7-.1 1.2-.6 1.4 1.7.2 2.3c.1.7-7.3 16.2-22.1 46.6-12.8 29.2-19.5 45.2-20.2 48-4.3 1.2-6.9 3-7.6 5.4.1.8 1.4 1.2 3.9 1.5l.1 1.1-1.1 1.9.2 2.3c.1 1.2 3.3 2.1 9.6 2.8 0-.4.2-.6.6-.7.1 1.4-.3 4.2-1.2 8.3l.7-.1c2.6-3.6 3.8-6 3.7-7.4l.5-.1c.6-.1 1.3.7 2.1 2.3-.8.3-3.6 6.6-8.3 18.8l.7 1.1.7-.1c5.8-14.7 9.5-22.8 10.9-24.4 7.6-.7 20.8-2 39.5-4l1.1-.7c0 .4.3.6.7.5l1.2-.1 1.1-.7c0 .4.3.6.7.5l14.2-1.2c3.7-.3 5.6-1.5 5.4-3.4-.1-.5 2.2-1.1 6.7-1.7.4 0 1 .1 1.9.3 0-.3 2.2-.7 6.7-1.1.1-2.3.9-3.6 2.3-3.7l-.1-1.1c-1.2.1-2.7.4-4.3 1-3-.2-4.9-.3-5.6-.7-.8.4-1.4.7-1.8.7l-9.9.3-1.2.6c0-.3-.3-.5-.7-.4l-.5.1-1.3.6c0-.3-1-.6-3.1-.9l-1.2.7-2.4.2-.1-.6c12.7-1.4 19-3.1 18.8-5.2.8-.1 1.2.3 1.3 1.1l3.7-1-.1-1.1-30.4.9c0 .4-.2.6-.6.7l-3.7-.3c-1 .1-1.7.3-2.3.9l-1.4-.5c-3.8.3-6.7.8-8.6 1.4-2.6-.2-5.5-.2-8.7.1l-6.9.6-3.1-.3c-.2-2 3.7-11.9 11.8-29.7 2.3-6 4.5-9.7 6.5-11.1.4 0 1 .1 1.9.4l4.9-1.7 2.5.4c4.2-.3 9-1.6 14.6-3.6 1.4-.1 2.4 0 3.1.3l3.7-.9 11-1c4.7-.4 9.8-1.5 15.3-3.1l-.1-1.1c-2.1.2-3.1-.2-3.2-1-.2-1.6-1.7-2.2-4.5-1.9l-.1-1.1 3-.9 8-.7-.1-1.1c-.3 0-1.9 0-5-.1l-.1-1.2 3.1-.3c.8-.1 1.2-.5 1.1-1.2-4.2.1-6.3-.1-6.3-.6-28.9 1.4-43.3 1.7-43.4.8-.4 0-1 .3-1.8.8l-.8-1.2c7.6-16.2 12.2-24.5 13.5-24.6.1.6-.3 1.7-1 3l.1.5 1.2-.1c3-7.3 5.6-11 7.7-11.2 9-.8-1.3-1.4 18.2-4.4.8.3 1.4.5 1.7.4 7.6-1.6 18.2-3.1 31.9-4.8l-.1-1.1-7.3.7-.1-1.1c.9-.1 1.6-.3 2.2-.9l.1.6c.4 0 1-.3 1.7-.8 1.2-.1 2.2 0 2.9.3 2.5-.7 4.5-1 6.1-1.2l4.5-.4c1.1-.7 1.7-1.3 1.6-1.9v-.5c-1.1.1-2.4 0-4-.3-.1-.6.3-1 1-1.2l8.4-.8 3.9-1c0 .4.3.6.5.6.4 0 1-.3 1.7-.8 2 .3 3.2.3 3.5.3 1.6-1.5 3.4-2.7 5.4-3.5-.9-.9-1.3-1.6-1.4-2.2l-5.6.5c0-.4-.3-1-.6-1.7 0-.5 1.2-1 3.8-1.6l1.1.4 1-.6c0 .3.3.5.6.4l1-.6c0 .3.3.5.6.4l.5-.1-.1-1.1c-.8.1-2.9.1-6.2 0l-1 .7-.1-.6c-26.5 2.2-41.5 3.4-45.1 3.7-.4 0-2.3 0-5.6-.1 0 .4-.3.7-1.1.7l-1.1-.5-3.9 1c-.9.1-2.4 0-4.6-.2-.6.4-1.3.8-2.2.9s21-1.2 20.3-1.6l-3.4 1-.5.1c-1.4.1-3.1.1-5.2-.2-.6.4-1.5.8-2.8.9-.9.1-1.7-.1-2.3-.4-.8.2-3.6.9-8.3 1.8zm1.4 3.5c0 .4.4.6 1.1.5l.1 1.1-1.1.1c-.9.1-1.7-.1-2.3-.4-.6.5-1.4.8-2.2.9h.3l-3.1-.3-.1-.5c3.9-.4 5.1-.8 7.3-1.4zm2.9-.3l.6-.1.1 1.1-1.7.2c-.1-.6.2-1 1-1.2zm6.7-.7l.1.6c-1.3.1-3 .4-5 1l-.6.1.5-1.2 5-.5zm-49.4 5.7l1.9-.2-.1-1.1-1.9.2.1 1.1zm13.7-1.2l.7-.1 1.3.5c.1.8-.3 1.2-1.1 1.2l-1.9.2-.1-.6c-.1-.6.3-1 1.1-1.2zm43.7 2.5v.5l-3.9.3v-.5l3.9-.3zm-11.2 1.6l.1.6-2.3.2-.1-.6 2.3-.2zm10.7.9l7.8-1.3 4 .2.1.6c-1.9.2-3.6.5-5 1 0-.4-.8-.8-2.3-1l-6.2 1.7c-1.2-.7-1.8-1.2-1.8-1.6l3.4.4zm-7.7 1.7c.8-.1 1.1.3 1.2 1l-3.3.3-.1-1.1 2.2-.2zm-6.9-.3l.1.6 20.3-1-.1-.6-20.3 1zm1.3 1.5v.5l-4.4.4v-.5l4.4-.4zm-29.1 23.1l.1.6 1.3-.1c0-.4.1-1 .4-1.8l-.1-1.1c-.7 0-1.3.8-1.7 2.4zm-44.8 64.9l.1.5c.1.8-.3 1.2-1.2 1.3l-.1-.5c-.1-.7.4-1 1.2-1.3zm-1.1 2.5c.1 1.1-.7 3.9-2.3 8.4l-1.9.2c-.1-.8 1-3.6 3-8.4l1.2-.2zm20.9 18.1c.1.6-1 1.3-3 2l-3.8-.3-1.2.1-.1-1.1 8.1-.7zm3.8.4l.1.5-3.1.3-.1-.5 3.1-.3z' }));
        case 'css': return React.createElement('g', null, React.createElement('title', null, 'CSS3'), React.createElement('path', { d: 'M104.5 89.2l-14.4 72h293.2l-9.1 46.5H80.8l-14.2 72h293.2l-16.4 82.1-118.2 39.1-102.4-39.1 7-35.6h-72l-17.1 86.4 169.3 65 195.2-64.8 25.9-130 5.3-26.1 33.3-167.4H104.5z' })); case 'express': return React.createElement('g', null, React.createElement('title', null, 'Express JS'), React.createElement('path', { d: 'M90.6 326.8v3.4H8.5V207.6H90v3.4H12v53.6h73.4v3.4H11.9v58.8h78.7z' }), React.createElement('path',
          { d: 'M160.9 241.9l-32.3 42.6 35.4 45.7h-4.3l-33.3-43.1-33.2 43.1h-4.1l35.2-45.7-32.1-42.6h4.5l29.7 40.1 30.4-40.1h4.1z' }), React.createElement('path', { d: 'M170.9 360.5V241.9h3.4v24.8h.3c1.1-3.9 2.8-7.5 5-10.7 2.2-3.3 4.8-6.1 7.7-8.5 3-2.4 6.4-4.3 10.2-5.6 3.8-1.3 8-2 12.5-2 5.8 0 11.1 1.1 15.7 3.4s8.6 5.5 11.9 9.6c3.3 4.1 5.8 9 7.5 14.6 1.7 5.6 2.6 11.7 2.6 18.4 0 6.2-.8 12.1-2.5 17.7s-4.1 10.5-7.2 14.8c-3.2 4.2-7.1 7.6-11.8 10.1-4.7 2.5-10.1 3.7-16.2 3.7-9.4 0-17.1-2.4-23.2-7.3-6.1-4.9-10.1-11.3-12.2-19.3h-.3v55h-3.4zm54.7-35.5c4.3-2.6 7.8-5.9 10.6-10s4.8-8.7 6.1-13.8c1.3-5.1 2-10.2 2-15.4 0-5.7-.7-11.1-2.1-16.2-1.4-5.1-3.6-9.6-6.4-13.6-2.8-4-6.4-7.1-10.7-9.4-4.3-2.3-9.3-3.4-15-3.4-5.6 0-10.7 1.1-15.1 3.4-4.5 2.3-8.2 5.4-11.3 9.3-3 3.9-5.4 8.4-7 13.6-1.6 5.2-2.4 10.6-2.4 16.3 0 13.9 3.3 24.5 9.8 31.9 6.5 7.4 15.2 11.1 26 11.1 6.1.1 11.2-1.2 15.5-3.8z' }),
        React.createElement('path', { d: 'M263.2 330.3v-88.4h3.4v21.8h.3c.8-3.3 2.1-6.4 3.8-9.4 1.7-2.9 4-5.5 6.9-7.6 2.9-2.2 6.3-3.9 10.2-5.2 4-1.3 8.6-1.8 13.8-1.7v3.4c-6.1-.3-11.3.5-15.7 2.5-4.4 2-8.1 4.8-10.9 8.3-2.9 3.5-5 7.5-6.4 12.1-1.4 4.6-2.1 9.3-2.1 14.1v50h-3.3z' }), React.createElement('path', { d: 'M308.1 303.4c1.3 5.1 3.4 9.5 6.1 13.3s6.3 6.8 10.7 8.9c4.4 2.2 9.7 3.3 16 3.3 9.3 0 17-2.4 23.1-7.3 6.1-4.9 9.9-12.2 11.4-21.9h3.4c-1.3 10.3-5.3 18.3-12.2 24s-15.4 8.5-25.4 8.5c-6.9.1-12.7-1-17.6-3.3-4.9-2.3-8.9-5.5-11.9-9.7-3.1-4.2-5.4-9.1-6.9-14.8-1.5-5.7-2.2-11.8-2.2-18.3 0-7.3 1.1-13.9 3.3-19.6 2.2-5.7 5-10.6 8.6-14.5 3.6-4 7.7-7 12.4-9 4.7-2.1 9.5-3.1 14.4-3.1 6.9 0 12.8 1.3 17.8 4 5 2.6 9.1 6.1 12.2 10.5 3.1 4.4 5.4 9.3 6.9 15 1.4 5.6 2 11.5 1.8 17.5h-74c.1 5.9.8 11.4 2.1 16.5zm66.1-35.4c-1.5-4.9-3.7-9.1-6.6-12.8-2.9-3.7-6.6-6.6-10.9-8.7-4.4-2.1-9.4-3.2-15.1-3.2-4.8 0-9.3.9-13.5 2.8-4.2 1.8-7.9 4.5-11 8-3.2 3.5-5.7 7.7-7.6 12.6-2 4.9-3.1 10.5-3.4 16.7h70.5c-.1-5.4-.9-10.6-2.4-15.4z' }),
        React.createElement('path', { d: 'M449.2 256.7c-1.4-3.1-3.3-5.6-5.8-7.6s-5.5-3.5-9-4.4c-3.5-.9-7.4-1.4-11.6-1.4-6.2 0-11.1.8-14.6 2.5-3.6 1.7-6.2 3.6-7.9 5.7-1.7 2.1-2.8 4.2-3.3 6.3-.5 2.1-.7 3.5-.7 4.3 0 4.4.9 7.7 2.7 10.1 1.8 2.4 4.2 4.2 7.1 5.4 3.1 1.3 6.4 2.4 10.1 3.3 3.6.9 7.8 2 12.6 3.3 3.4.9 6.8 1.9 10.2 2.8 3.4 1 6.4 2.3 9.1 4 2.7 1.7 4.9 3.9 6.5 6.5 1.7 2.6 2.5 6.1 2.5 10.3 0 4.5-.9 8.3-2.8 11.3-1.9 3.1-4.4 5.6-7.4 7.6s-6.5 3.4-10.3 4.2c-3.8.9-7.6 1.3-11.4 1.3-11.8 0-20.7-2.6-26.7-7.7-6-5.2-9-13.3-9-24.4h3.4c0 10 2.8 17.3 8.3 21.9 5.6 4.6 13.5 7 24 7 3.2 0 6.5-.4 9.9-1.1 3.4-.7 6.4-1.9 9.2-3.6 2.8-1.7 5-3.8 6.8-6.4 1.8-2.6 2.7-5.8 2.7-9.6 0-3.9-.9-7-2.7-9.5-1.8-2.4-4.1-4.4-6.9-5.9-2.8-1.5-6-2.8-9.7-3.7-3.7-.9-7.4-1.8-11.2-2.8-4.2-1-8-2-11.2-2.9-3.2-.9-6.1-1.9-8.6-3.1-3.7-1.6-6.4-3.8-8.1-6.7-1.7-2.9-2.6-6.7-2.6-11.5 0-2.4.5-4.9 1.5-7.6 1-2.6 2.8-5 5.2-7.2 2.4-2.2 5.5-4 9.3-5.3 3.8-1.4 8.4-2.1 13.9-2.1 9.9 0 17.6 2.2 23.4 6.7 5.7 4.5 8.6 11.6 8.6 21.5h-3.4c-.1-4.6-.8-8.4-2.1-11.5z' }),
        React.createElement('path', { d: 'M528.8 256.7c-1.4-3.1-3.3-5.6-5.8-7.6s-5.5-3.5-9-4.4c-3.5-.9-7.4-1.4-11.6-1.4-6.2 0-11.1.8-14.6 2.5-3.6 1.7-6.2 3.6-7.9 5.7-1.7 2.1-2.8 4.2-3.3 6.3-.5 2.1-.7 3.5-.7 4.3 0 4.4.9 7.7 2.7 10.1 1.8 2.4 4.2 4.2 7.1 5.4 3.1 1.3 6.4 2.4 10.1 3.3 3.6.9 7.8 2 12.6 3.3 3.4.9 6.8 1.9 10.2 2.8 3.4 1 6.4 2.3 9.1 4 2.7 1.7 4.9 3.9 6.5 6.5 1.7 2.6 2.5 6.1 2.5 10.3 0 4.5-.9 8.3-2.8 11.3-1.9 3.1-4.4 5.6-7.4 7.6s-6.5 3.4-10.3 4.2c-3.8.9-7.6 1.3-11.4 1.3-11.8 0-20.7-2.6-26.7-7.7-6-5.2-9-13.3-9-24.4h3.4c0 10 2.8 17.3 8.3 21.9 5.6 4.6 13.5 7 24 7 3.2 0 6.5-.4 9.9-1.1 3.4-.7 6.4-1.9 9.2-3.6 2.8-1.7 5-3.8 6.8-6.4 1.8-2.6 2.7-5.8 2.7-9.6 0-3.9-.9-7-2.7-9.5-1.8-2.4-4.1-4.4-6.9-5.9-2.8-1.5-6-2.8-9.7-3.7-3.7-.9-7.4-1.8-11.2-2.8-4.2-1-8-2-11.2-2.9-3.2-.9-6.1-1.9-8.6-3.1-3.7-1.6-6.4-3.8-8.1-6.7-1.7-2.9-2.6-6.7-2.6-11.5 0-2.4.5-4.9 1.5-7.6 1-2.6 2.8-5 5.2-7.2 2.4-2.2 5.5-4 9.3-5.3 3.8-1.4 8.4-2.1 13.9-2.1 9.9 0 17.6 2.2 23.4 6.7 5.7 4.5 8.6 11.6 8.6 21.5h-3.4c-.1-4.6-.8-8.4-2.1-11.5z' }));
        case 'graphql': return React.createElement('g', null, React.createElement('title', null, 'GraphQL'), React.createElement('path', { d: 'M274.432 17.65l19.83 11.45L73.413 411.61l-19.83-11.45z' }), React.createElement('path', { d: 'M49.1 369.6h441.7v22.9H49.1z' }), React.createElement('path', { d: 'M69.33 362.572l220.918 127.55-11.45 19.83L57.88 382.403z' }), React.createElement('path', { d: 'M261.207 30.163l220.917 127.55-11.45 19.83-220.917-127.55z' }), React.createElement('path', { d: 'M278.806 30.038l11.45 19.83L69.34 177.42l-11.45-19.83z' }),
          React.createElement('path', { d: 'M265.564 17.688L486.414 400.2l-19.83 11.45-220.85-382.512z' }), React.createElement('path', { d: 'M66.6 142.4h22.9v255.1H66.6z' }), React.createElement('path', { d: 'M450.5 142.4h22.9v255.1h-22.9z' }), React.createElement('path', { d: 'M457.01 371.982l10 17.32-192.167 110.95-10-17.32z' }), React.createElement('path', { d: 'M503.7 405c-13.2 23-42.7 30.9-65.8 17.7-23-13.2-30.9-42.7-17.7-65.8 13.2-23 42.7-30.9 65.8-17.7 23.2 13.4 31.1 42.8 17.7 65.8' }), React.createElement('path', { d: 'M119.6 183.1c-13.2 23-42.7 30.9-65.8 17.7-23-13.2-30.9-42.7-17.7-65.8s42.7-30.9 65.8-17.7c23 13.4 30.9 42.8 17.7 65.8' }),
          React.createElement('path', { d: 'M36.3 405c-13.2-23-5.4-52.4 17.7-65.8 23-13.2 52.4-5.4 65.8 17.7 13.2 23 5.4 52.4-17.7 65.8-23.2 13.2-52.6 5.3-65.8-17.7' }), React.createElement('path', { d: 'M420.4 183.1c-13.2-23-5.4-52.4 17.7-65.8 23-13.2 52.4-5.4 65.8 17.7 13.2 23 5.4 52.4-17.7 65.8-23 13.2-52.5 5.4-65.8-17.7' }), React.createElement('path', { d: 'M270 540c-26.6 0-48.1-21.5-48.1-48.1s21.5-48.1 48.1-48.1 48.1 21.5 48.1 48.1c0 26.5-21.5 48.1-48.1 48.1' }), React.createElement('path', { d: 'M270 96.3c-26.6 0-48.1-21.5-48.1-48.1S243.4 0 270 0s48.1 21.5 48.1 48.1-21.5 48.2-48.1 48.2' }));
        case 'html': return React.createElement('g', null, React.createElement('title', null, 'HTML5'), React.createElement('path', { d: 'M169.3 64.1h-20.5V42h-22.4v67h22.4V86.6h20.5V109h22.4V42h-22.4v22.1zm32.1.1h19.7V109h22.4V64.2h19.7V42h-61.8v22.2zm109.3 1.3L296.4 42H273v67h21.9V75.8l15.4 23.8h.4l15.4-23.8V109h22.3V42H325l-14.3 23.5zM382 86.8V42h-22.4v67h53.9V86.8H382z' }), React.createElement('path', { d: 'M94.1 138.4l32 359.2 143.8 39.9 143.9-39.9 32.1-359.2H94.1zm292.3 336.3L270 507l-116.4-32.3-27.4-307h287.5l-27.3 307z' }),
          React.createElement('path', { d: 'M126.2 167.7l27.4 307L270 507v-45.7h-.1l-90.3-25.1-6.3-70.7h44.2l3.3 36.7 49.1 13.3h.1V345h-98.5l-11.9-133.2H270v-44.1m0 133.3v-45.1h-62.2l4 45.1H270z' }), React.createElement('path', { d: 'M324.2 345H270v70.4l49.1-13.3 5.1-57.1zM270 167.7v44.1h110.3l-3.9 44.1H270V301h102.4l-12.1 135.3-90.3 25V507l116.4-32.3 27.4-307H270z' })); case 'javascript': return React.createElement('g', null, React.createElement('title', null, 'JavaScript'), React.createElement('path', { d: 'M79 79v382h382V79H79zm201.3 327.2c-5.6 11.4-16.4 19-28.8 22.6-19.1 4.4-37.4 1.9-51.1-6.3-9.1-5.6-16.2-14.2-21.1-24.1 9.7-5.9 19.4-11.9 29-******** 1 1.5 2 3.3 3.7 6.2 6.9 10.6 13.2 13.7 6.2 2.1 19.7 3.4 24.9-7.4 3.2-5.5 2.2-23.5 2.2-43.2l.1-92.4h35.7c0 32.9.2 65.6 0 98.4.3 20 2.1 38.2-6.1 53.2zm148.3-10.1c-12.4 42.5-81.7 43.9-109.3 15.8-5.8-6.6-9.5-10.1-13-17.7 14.7-8.5 14.7-8.5 29-16.7 7.8 11.9 15 18.5 27.9 21.2 17.5 2.1 35.2-3.9 31.2-22.5-4.1-15.2-35.9-18.9-57.6-35.2-22-14.8-27.2-50.7-9.1-71.2 6-7.6 16.3-13.3 27.1-16 3.7-.5 7.5-1 11.3-1.5 21.6-.4 35.1 5.3 45 16.3 2.8 2.8 5 5.8 9.2 12.3-11.5 7.3-11.5 7.3-28 17.9-3.5-7.6-9.4-12.3-15.5-14.4-9.6-2.9-21.7.3-24.2 10.4-.9 3.1-.7 6 .7 11.2 3.9 8.9 16.9 12.7 28.6 18.1 33.7 13.7 45 28.3 47.8 45.7 2.7 15.1-.6 24.8-1.1 26.3z' }));
        case 'json': return React.createElement('g', null, React.createElement('title', null, 'JSON'), React.createElement('linearGradient', {
          id: 'jsonGradient1', gradientUnits: 'userSpaceOnUse', x1: '67.464', y1: '92.305', x2: '421.759', y2: '446.6',
        }, React.createElement('stop', { offset: '0', stopColor: 'currentColor' }), React.createElement('stop', { offset: '1', stopColor: 'currentColor', stopOpacity: '0' })), React.createElement('path', { fill: 'url(#jsonGradient1)', d: 'M93.8 307.9c0-130.3 96.7-195.4 175.6-168.2 0 0-.1 0-.2.1h.1c1.6.6 79.4 31.3 79.4 131.5 0 99.8-79.1 130.7-79.1 130.7l.1.1c.1 0 .2.1.3.1 70.1 29.9 178-40.1 178-168.9 0-161-96.1-217.4-149.3-230.2-10.2-1.8-20-2.8-29.1-2.8C131.4.3 0 114.4 0 270.5 0 443.9 150.7 540 269.4 540h-.2c-23.8-.8-175.4-23.5-175.4-232.1z' }),
        React.createElement('linearGradient', {
          id: 'jsonGradient2', gradientUnits: 'userSpaceOnUse', x1: '-216.621', y1: '-295.862', x2: '-597.461', y2: '85.033', gradientTransform: 'matrix(.9988 0 0 -.9987 689.01 152.452)',
        }, React.createElement('stop', { offset: '0', stopColor: 'currentColor' }), React.createElement('stop', { offset: '1', stopColor: 'currentColor', stopOpacity: '0' })), React.createElement('path', { fill: 'url(#jsonGradient2)', d: 'M269.4 139.7c-78.9-27.2-175.6 37.9-175.6 168.2C93.8 520.7 251.4 540 270.6 540c138 0 269.4-114.1 269.4-270.1C540 96.4 389.3.3 270.6.3c32.9-4.6 177.3 35.6 177.3 233 0 128.7-107.8 198.8-178 168.9-1.6-.6-79.4-31.3-79.4-131.5 0-99.7 78.9-131 78.9-131z' }));
        case 'jwt': return React.createElement('g', null, React.createElement('title', null, 'Json Web Tokens'), React.createElement('g', null, React.createElement('path', { d: 'M310.9 148l-.6-142h-79.2l.5 142 39.6 54.4 39.7-54.4z' }), React.createElement('path', { d: 'M231.7 391.4V534h79.2V391.4L271.3 337l-39.6 54.4z' })), React.createElement('g', { opacity: '.8' }, React.createElement('path', { d: 'M166.2 345L82.8 460.1l63.9 46.5 84-115.1v-67.1L166.2 345z' }), React.createElement('path', { d: 'M373.8 194.5l83.4-115.1-63.9-46.5L309.9 148v67.1l63.9-20.6z' })),
          React.createElement('g', { opacity: '.7' }, React.createElement('path', { d: 'M398 270l135.2-44.4-24.3-75-135.2 43.8-39.6 54.4L398 270z' }), React.createElement('path', { d: 'M142 270L6.8 313.8l24.3 75L166.2 345l39.6-54.4L142 270z' })), React.createElement('g', { opacity: '.5' }, React.createElement('path', { d: 'M309.9 391.4l83.4 115.1 63.9-46.5-83.4-115-63.9-20.6v67z' }), React.createElement('path', { d: 'M230.7 148l-84-115.1-63.9 46.5 83.4 115.1 64.4 20.6V148z' })), React.createElement('g', { opacity: '.4' }, React.createElement('path',
            { d: 'M166.2 194.5L31.1 150.7l-24.3 75L142 270l63.9-21.1-39.7-54.4z' }), React.createElement('path', { d: 'M334.2 290.6l39.6 54.4L509 388.8l24.3-75L398 270l-63.8 20.6z' }))); case 'koa': return React.createElement('g', null, React.createElement('title', null, 'Koa JS'), React.createElement('path', { d: 'M50.6 158.8v139.7l102.1-139.7 2.5 2.2-56.6 77.1 84 141.9h-29.4L82.5 260.2l-31.9 43.9V380H24.3V158.8h26.3z' }), React.createElement('path', { d: 'M273.5 381.2c-22.2 0-40.3-6.3-54.2-19-13.9-12.6-20.9-32.1-20.9-58.5 0-26.3 7.2-46.8 21.6-61.3 14.4-14.5 32.8-21.8 55-21.8 22.2 0 40.3 6.3 54.2 19 13.9 12.6 20.9 32.1 20.9 58.5 0 26.3-7.2 46.8-21.6 61.3-14.5 14.6-32.8 21.8-55 21.8zm2-158c-16 0-29.3 7.1-39.8 21.3-10.5 14.2-15.8 34-15.8 59.4s4.8 44.2 14.5 56.4c9.7 12.2 22.5 18.3 38.5 18.3s29.3-7.1 39.8-21.3c10.5-14.2 15.8-34 15.8-59.4s-4.8-44.2-14.5-56.4c-9.6-12.2-22.5-18.3-38.5-18.3z' }),
          React.createElement('path', { d: 'M505.9 380c-14.3 0-24.1-2.7-29.4-8.2-4.8-4.6-7.3-8.6-7.3-12v-6.6c-10.3 18.8-26.9 28.1-49.6 28.1-26.5 0-41.6-12-45.2-36-.4-2.5-.6-5.1-.6-7.7 0-2.6.4-5.5 1.3-8.7.8-3.2 3.3-6.6 7.3-10.4 8-7.6 25.6-12.2 52.8-13.9 6.7-.6 13-.9 18.6-.9 5.7 0 10.8.2 15.5.6v-52.4c-.4-.2-.4-1.2 0-3s0-4.1-1.1-7c-1.2-2.8-2.7-5.6-4.6-8.4-1.9-2.7-5.3-5.1-10.1-7.1s-11.2-3-19-3c-7.8 0-16.9 1.4-27.3 4.3-10.4 2.8-18.1 5.6-22.9 8.4l-1.3-2.2c18.7-8.6 37.8-13 57.2-13 21.7 0 36 3.9 43 11.7 5.5 6.1 8.2 12.5 8.2 19.3v105.8c0 6.1 1.2 10.8 3.6 14.1 2.4 3.3 4.9 5 7.4 5.2l3.5.6h9.8v2.5h-9.8zm-81.2-1.3c11.2 0 21.2-3.8 30.2-11.5 8.9-7.7 13.7-15.1 14.4-22.3v-38.2c-5.7-.4-11.4-.6-17.2-.6-5.8 0-11.5.3-17.2.9-16 1.9-26.4 5.5-31.3 10.7-4.8 5.3-7.3 12.5-7.3 21.8 0 1.7.1 3.6.3 5.7 1.8 22.3 11.2 33.5 28.1 33.5z' }));
        case 'medium': return React.createElement('g', null, React.createElement('title', null, 'Medium'), React.createElement('path', { d: 'M198.7 160.9v269.5c0 3.8-1 7.1-3 9.8-2 2.7-4.9 4-8.7 4-2.7 0-5.4-.6-7.9-1.8L68.3 388.8c-3.3-1.5-6.2-4.1-8.5-7.7-2.3-3.6-3.5-7.2-3.5-10.7V108.5c0-3.1.8-5.7 2.4-7.8 1.6-2.1 3.9-3.2 6.9-3.2 2.2 0 5.7 1.2 10.5 3.5L198 159.7c.5.5.7.9.7 1.2zm15.3 23.2L341.3 383 214 321.9V184.1zm268.9-23.4c0 .5-20.4 32.6-61.1 96.4-40.8 63.8-64.7 101.1-71.7 112l-93-145.7 77.2-121.1c2.7-4.3 6.8-6.4 12.4-6.4 2.2 0 4.3.5 6.2 1.4l129 62c.7.3 1 .7 1 1.4zm.7 27.5v242.1c0 3.8-1.1 6.9-3.3 9.3-2.2 2.4-5.3 3.6-9.1 3.6s-7.5-1-11.2-3l-105.1-50.5 128.7-201.5z' }));
        case 'mongo': return React.createElement('g', null, React.createElement('title', null, 'Mongo DB'), React.createElement('path', { opacity: '.9', d: 'M271.3 403.5c-2.8 35.5-6.8 61.3-10.7 70.4 0 0-.5-.4-1.5-1.1 9.9 23.1 8.9 62.4 8.9 62.4l14.5 4.8s-2.9-38.3 1.2-56.8c1.2-5.6 4.1-10.5 7.5-14.6-.5.4-.8.6-.8.6-14.4-6.5-18.5-37.1-19.1-65.7zm38 50.1l-.6.6.6-.3v-.3z' }), React.createElement('path', { opacity: '.3', d: 'M386.6 214.2C358.2 89.1 291.1 48 283.9 32.3 276 21.2 268 1.5 268 1.5v.3c6.7 25.9 5.6 235.1 6.3 260.1 1.6 52.4 0 102.7-3.1 141.6.6 28.6 4.7 59.2 18.9 65.7.1 0 125.9-82.9 96.5-255z' }),
          React.createElement('path', { opacity: '.6', d: 'M260.5 473.8s-118.1-80.5-111.3-222.6c6.8-142.1 90.2-212 106.4-224.7 10.6-11.1 11-15.5 11.8-26.5 7.4 15.8 6.1 236.1 6.9 261.9 3.1 100.2-5.5 193-13.8 211.9z' })); case 'nginx': return React.createElement('g', null, React.createElement('title', null, 'Nginx'), React.createElement('path', { d: 'M493.7 126L282.7 4.2c-7.9-4.6-17.6-4.6-25.5 0L46.3 126c-7.9 4.6-12.7 13-12.7 22.1v243.8c0 9.1 4.9 17.5 12.7 22.1l211 121.9c7.9 4.6 17.6 4.6 25.5 0l211-121.9c7.9-4.6 12.7-13 12.7-22.1V148.1c0-9.1-4.9-17.5-12.8-22.1zM404 371.7c0 12.5-7.5 23.7-18.9 28.6s-24.7 2.4-33.7-6.3L193 240v134c0 17.1-13.9 31-31 31s-31-13.9-31-31V166.7c0-12.5 7.5-23.7 18.9-28.6 11.5-4.9 24.7-2.4 33.7 6.3L342 298.3V166c0-17.1 13.9-31 31-31s31 13.9 31 31v205.7z' }));
        case 'node': return React.createElement('g', null, React.createElement('title', null, 'Node JS'), React.createElement('path', { d: 'M488.7 119.6L290.8 5.2c-12.4-7.1-29.1-7.1-41.6 0L51.3 119.6c-12.8 7.4-20.8 21.2-20.8 36.1v228.4c0 14.8 7.9 28.7 20.8 36.1l51.9 29.9c25.1 12.4 34.2 12.4 45.6 12.4 37.2 0 58.6-22.5 58.6-61.8V175.2c0-3.2-2.6-5.7-5.7-5.7h-25.1c-3.2 0-5.8 2.5-5.8 5.7v225.5c0 17.4-18 34.7-47.4 20l-54.2-31.3c-1.9-1-3.1-3.1-3.1-5.3V155.7c0-2.2 1.2-4.3 3.1-5.4L266.9 36.1c1.8-1.1 4.3-1.1 6.1 0l197.9 114.1c1.9 1.1 3.1 3.2 3.1 5.5v228.4c0 2.2-1.2 4.3-3 5.4L273 503.7c-1.7 1-4.3 1-6.1 0l-50.8-30.1c-1.5-.9-3.5-1.2-4.9-.4-14.1 8-16.7 9-29.9 13.6-3.3 1.1-8.1 3.1 1.8 8.6l66.1 39.1c6.3 3.7 13.5 5.6 20.8 5.6 7.3 0 14.5-1.9 20.8-5.6l197.9-114.3c12.8-7.5 20.8-21.3 20.8-36.1V155.7c0-14.8-8-28.7-20.8-36.1zM331.1 347.9c-52.4 0-63.9-13.1-67.8-39.2-.4-2.8-2.8-4.8-5.7-4.8H232c-3.2 0-5.7 2.5-5.7 5.7 0 33.3 18.1 73.1 104.8 73.1 62.7 0 98.7-24.7 98.7-67.8 0-42.8-28.9-54.1-89.7-62.2-61.5-8.1-67.7-12.3-67.7-26.7 0-11.9 5.3-27.7 50.8-27.7 40.7 0 55.7 8.8 61.8 36.2.5 2.6 2.9 4.5 5.6 4.5h25.7c1.6 0 3.1-.7 4.2-1.8 1.1-1.2 1.7-2.8 1.5-4.4-4-47.2-35.4-69.2-98.8-69.2-56.4 0-90.1 23.8-90.1 63.8 0 43.3 33.5 55.3 87.7 60.6 64.8 6.3 69.8 15.8 69.8 28.6-.1 21.9-17.8 31.3-59.5 31.3z' }));
        case 'npm': return React.createElement('g', null, React.createElement('title', null, 'NPM'), React.createElement('path', { opacity: '.3', d: 'M13.7 184.7v170.8h142.4V384H270v-28.5h256.3V184.7H13.7z' }), React.createElement('path', { d: 'M326.9 213.2v113.9h56.9v-85.4h28.5v85.4h28.5v-85.4h28.5v85.4h28.5V213.2H326.9zM184.6 355.5h56.9V327h56.9V213.2H184.6v142.3zm56.9-113.9H270v56.9h-28.5v-56.9zM42.2 327.1h56.9v-85.4h28.5v85.4h28.5V213.2H42.2v113.9z' })); case 'passport': return React.createElement('g', null, React.createElement('path',
          { opacity: '.9', d: 'M221.8 399V207.5H126v287.3h191.5V399h-95.7z' }), React.createElement('path', { opacity: '.7', d: 'M317.5 16C211.7 16 126 101.7 126 207.5h95.8c0-52.9 42.9-95.8 95.8-95.8V16h-.1z' }), React.createElement('path', { opacity: '.5', d: 'M509 207.5C509 101.7 423.3 16 317.5 16v95.8c52.9 0 95.8 42.9 95.8 95.8H509v-.1z' }), React.createElement('path', { opacity: '.3', d: 'M317.5 399C423.3 399 509 313.3 509 207.5h-95.8c0 52.9-42.9 95.8-95.8 95.8V399h.1z' })); case 'react': return React.createElement('g', null, React.createElement('title',
          null, 'React'), React.createElement('circle', { cx: '270', cy: '270', r: '48.2' }), React.createElement('g', { fill: 'none', strokeWidth: '23.037' }, React.createElement('path', { d: 'M270 171.6c64.7 0 124.7 9.3 170 24.9 54.6 18.8 88.1 47.3 88.1 73 0 26.9-35.6 57.1-94.1 76.5-44.3 14.7-102.6 22.3-164 22.3-62.9 0-122.5-7.2-167.3-22.5-56.7-19.4-90.8-50-90.8-76.4 0-25.6 32-53.8 85.8-72.6 45.5-15.7 107-25.2 172.3-25.2z' }), React.createElement('path', { d: 'M185 221c32.3-56 70.3-103.4 106.5-134.8 43.5-37.9 85-52.7 107.3-39.8 23.3 13.4 31.7 59.3 19.3 119.8-9.4 45.7-31.9 100-62.6 153.2C324 373.8 288 421.8 252.4 453c-45.1 39.4-88.7 53.7-111.5 40.5-22.2-12.8-30.6-54.6-20-110.6 8.9-47.3 31.5-105.4 64.1-161.9z' }),
          React.createElement('path', { d: 'M184.5 319.1c-32.4-56-54.5-112.6-63.6-159.6-11.1-56.6-3.2-99.9 19.1-112.8 23.3-13.5 67.2 2.2 113.4 43.1 34.9 31 70.7 77.6 101.5 130.7 31.5 54.5 55.2 109.6 64.3 156.1 11.6 58.8 2.2 103.6-20.6 116.9-22.1 12.8-62.6-.8-105.8-37.9-36.6-31.5-75.6-80-108.3-136.5z' }))); case 'redux': return React.createElement('g', null, React.createElement('title', null, 'Redux'), React.createElement('path', { d: 'M365.4 328c18.3-1.9 32.1-17.6 31.5-36.5-.6-18.9-16.4-34-35.3-34h-1.3c-19.5.6-34.6 17-34 36.5.6 9.4 4.4 17.6 10.1 23.3-21.4 42.2-54.1 73-103.3 98.8-33.4 17.6-68 23.9-102.6 19.5-28.3-3.8-50.4-16.4-64.2-37.1-20.1-30.8-22-64.2-5-97.6 12-23.9 30.8-41.6 42.8-50.4-2.5-8.2-6.3-22-8.2-32.1C4.7 284.6 14.1 374 41.8 416.2c20.8 31.5 63 51 109.5 51 12.6 0 25.2-1.3 37.8-4.4C269.7 447 330.8 399.2 365.4 328z' }),
          React.createElement('path', { d: 'M476.2 249.9c-47.8-56-118.4-86.9-198.9-86.9h-10.1c-5.7-11.3-17.6-18.9-30.8-18.9h-1.3c-19.5.6-34.6 17-34 36.5.6 18.9 16.4 34 35.3 34h1.3c13.9-.6 25.8-9.4 30.8-21.4h11.3c47.8 0 93.2 13.9 134.1 40.9 31.5 20.8 54.1 47.8 66.7 80.6 10.7 26.4 10.1 52.3-1.3 74.3-17.6 33.4-47.2 51.6-86.3 51.6-25.2 0-49.1-7.6-61.7-13.2-6.9 6.3-19.5 16.4-28.3 22.7 27.1 12.6 54.8 19.5 81.2 19.5 60.4 0 105.1-33.4 122.1-66.7 18.4-36.5 17.2-99.4-30.1-153z' }), React.createElement('path', { d: 'M156.4 338.7c.6 18.9 16.4 34 35.3 34h1.3c19.5-.6 34.6-17 34-36.5-.6-18.9-16.4-34-35.3-34h-1.3c-1.3 0-3.1 0-4.4.6-25.8-42.8-36.5-89.4-32.7-139.8 2.5-37.8 15.1-70.5 37.1-97.6 18.3-23.3 53.5-34.6 77.4-35.3 66.7-1.3 95.1 81.8 97 115.2 8.2 1.9 22 6.3 31.5 9.4C388.7 52.9 325.8 0 265.3 0c-56.7 0-108.9 40.9-129.7 101.4-29 80.6-10.1 158 25.2 219.1-3.1 4.4-5 11.3-4.4 18.2z' }));
        case 'relay': return React.createElement('g', null, React.createElement('title', null, 'Relay'), React.createElement('path', { d: 'M116.8 171.6c0 25.6-20.8 46.4-46.4 46.4-25.6 0-46.4-20.7-46.4-46.4s20.8-46.4 46.4-46.4c25.7 0 46.4 20.8 46.4 46.4' }), React.createElement('path', { d: 'M469.6 381.1H209.8c-34.3 0-62.2-27.9-62.2-62.2s27.9-62.2 62.2-62.2h117.5c20.2 0 36.7-16.5 36.7-36.7s-16.5-36.7-36.7-36.7H70.4c-7 0-12.7-5.7-12.7-12.7 0-7 5.7-12.7 12.7-12.7h256.9c34.3 0 62.2 27.9 62.2 62.2s-27.9 62.2-62.2 62.2H209.8c-20.3 0-36.7 16.5-36.7 36.7 0 20.2 16.5 36.7 36.7 36.7h259.8c7 0 12.7 5.7 12.7 12.7 0 7-5.7 12.7-12.7 12.7z' }),
          React.createElement('path', { d: 'M423.2 368.4c0-25.6 20.8-46.4 46.4-46.4s46.4 20.8 46.4 46.4-20.8 46.4-46.4 46.4-46.4-20.8-46.4-46.4' })); case 'sass': return React.createElement('g', null, React.createElement('title', null, 'Sass'), React.createElement('path', { d: 'M459.1 289.2c-17.3.1-32.2 4.2-44.8 10.4-4.6-9.2-9.2-17.2-10-23.2-.9-7-2-11.2-.9-19.5s5.9-20.1 5.8-21c-.1-.9-1.1-5.1-11-5.2-10-.1-18.5 1.9-19.5 4.5s-2.9 8.5-4.1 14.7c-1.7 9-19.9 41.2-30.2 58.1-3.4-6.6-6.2-12.4-6.8-17-.9-7-2-11.2-.9-19.5s5.9-20.1 5.8-21c-.1-.9-1.1-5.1-11-5.2-10-.1-18.5 1.9-19.5 4.5s-2.1 8.8-4.1 14.7c-2 5.9-26.2 59.7-32.5 73.6-3.2 7.1-6 12.8-8 16.7s-.1.3-.3.7c-1.7 3.3-2.7 5.1-2.7 5.1v.1c-1.3 2.4-2.8 4.7-3.5 4.7-.5 0-1.5-6.5.2-15.4 3.6-18.6 12.2-47.7 12.1-48.7 0-.5 1.6-5.6-5.6-8.2-7-2.6-9.5 1.7-10.2 1.7-.6 0-1.1 1.5-1.1 1.5s7.8-32.7-14.9-32.7c-14.2 0-34 15.6-43.7 29.7-6.1 3.3-19.3 10.5-33.2 18.2-5.3 2.9-10.8 5.9-16 8.8-.4-.4-.7-.8-1.1-1.2-27.4-29.2-78.4-50-76.2-89.6.8-14.4 5.8-52.3 98.1-98.3 75.6-37.7 136.1-27.3 146.5-4.3 14.9 32.8-32.3 93.8-110.8 102.6-29.9 3.4-45.7-8.2-49.6-12.6-4.1-4.5-4.7-4.7-6.3-3.9-2.5 1.4-.9 5.4 0 7.8 2.3 6.1 12 16.9 28.4 22.3 14.4 4.7 49.5 7.3 92-9.1 47.6-18.4 84.7-69.6 73.8-112.3-11.2-43.5-83.3-57.8-151.6-33.6-40.6 14.4-84.6 37.1-116.2 66.7-37.6 35.2-43.6 65.8-41.1 78.6 8.8 45.4 71.4 75 96.5 97-1.2.7-2.4 1.3-3.5 1.9-12.6 6.2-60.3 31.2-72.3 57.6-13.5 30 2.2 51.4 12.6 54.3 32.2 9 65.3-7.2 83-33.7 17.8-26.5 15.6-61 7.4-76.7l-.3-.6c3.2-1.9 6.6-3.9 9.8-5.8 6.4-3.8 12.7-7.3 18.1-10.2-3.1 8.4-5.3 18.4-6.4 32.9-1.4 17 5.6 39 14.7 47.6 4 3.8 8.8 3.9 11.9 3.9 10.6 0 15.5-8.8 20.8-19.3 6.5-12.8 12.3-27.7 12.3-27.7s-7.3 40.3 12.6 40.3c7.2 0 14.5-9.4 17.7-14.2v.1l.6-.9c.8-1.1 1.2-1.9 1.2-1.9v-.2c2.9-5 9.3-16.5 19-35.5 12.5-24.5 24.5-55.2 24.5-55.2s1.1 7.5 4.8 19.9c2.1 7.3 6.7 15.4 10.3 23.1-2.9 4-4.7 6.3-4.7 6.3v.1c-2.3 3.1-4.9 6.4-7.6 9.6-9.9 11.7-21.6 25.1-23.2 29-1.9 4.6-1.4 7.9 2.2 10.6 2.6 2 7.3 2.3 12.1 1.9 8.9-.6 15.1-2.8 18.2-4.1 4.8-1.7 10.4-4.4 15.6-8.2 9.7-7.1 15.5-17.3 14.9-30.7-.3-7.4-2.7-14.8-5.7-21.7.9-1.3 1.8-2.5 2.6-3.8 15.2-22.3 27.1-46.8 27.1-46.8s1.1 7.5 4.8 19.9c1.8 6.3 5.5 13.1 8.8 19.9-14.3 11.6-23.2 25.2-26.3 34-5.7 16.4-1.2 23.9 7.1 25.5 3.8.8 9.2-1 13.2-2.7 5-1.7 11-4.4 16.7-8.6 9.7-7.1 19-17.1 18.4-30.5-.3-6.1-1.9-12.2-4.2-18.1 12.1-5.1 27.9-7.9 47.9-5.5 43 5 51.4 31.8 49.8 43.1-1.6 11.2-10.6 17.4-13.6 19.3-3 1.9-3.9 2.5-3.7 3.9.4 2 1.8 1.9 4.3 1.5 3.5-.6 22.6-9.1 23.4-29.9 1.1-26.2-24.1-55.1-68.7-54.9zM127.9 400.9c-14.2 15.5-34.1 21.4-42.6 16.5-9.2-5.3-5.6-28.2 11.9-44.7 10.6-10 24.4-19.3 33.5-25 2.1-1.2 5.1-3.1 8.8-5.3.6-.4 1-.5 1-.5.7-.4 1.4-.9 2.2-1.3 6.3 23.3.1 44-14.8 60.3zm103.7-70.5c-5 12.1-15.3 43-21.6 41.3-5.4-1.4-8.7-24.9-1.1-48.1 3.8-11.6 12.1-25.6 16.9-31 7.8-8.7 16.3-11.5 18.4-8 2.6 4.6-9.5 38.2-12.6 45.8zm85.6 40.9c-2.1 1.1-4 1.8-4.9 1.3-.7-.4.9-1.8.9-1.8s10.7-11.5 14.9-16.8c2.5-3.1 5.3-6.7 8.4-10.7v1.2c0 13.7-13.3 23-19.3 26.8zm66-15.1c-1.6-1.1-1.3-4.7 3.8-16 2-4.4 6.6-11.8 14.7-18.9.9 2.9 1.5 5.7 1.5 8.3-.1 17.4-12.5 23.9-20 26.6z' }));
        case 'sketch': return React.createElement('g', null, React.createElement('tile', null, 'Sketch'), React.createElement('path', { d: 'M453.4 103.9l-91.7-39H178.2l-91.8 39-72.5 84.5 256 349 256-349-72.5-84.5zm50.3 85.5l-93.4 71.1-43.9-98L447 123l56.7 66.4zm-110.5 74.9H145.8l42.7-95.4h162.1l42.6 95.4zm-212-182h176.6l71.3 30.3-78.5 39H188.4l-78.5-39 71.3-30.3zM92 123l80.6 39.5-44 98-93.4-71.1L92 123zM71.1 238.3l53.8 40.8 68.3 125.3L71.1 238.3zm198.4 270.2L145.8 281.7h247.5L269.5 508.5zm76.4-104.1l68.3-125.3 53.8-40.8-122.1 166.1z' }));
        case 'socket': return React.createElement('g', null, React.createElement('title', null, 'Socket.IO'), React.createElement('path', { d: 'M539.9 263.9c-1.3-90.4-52-178.2-129.6-224.6-61.6-38-138.8-48.4-208.7-30.5C96.3 35 12.8 130.4 2.1 238.5c-13.3 102.4 38.4 209 127 261.8 86.9 54.4 204.6 52.2 289.7-5 76.4-49.6 123.9-140.1 121.1-231.4zM285 499.8C162.3 512.1 43.7 408 40.2 284.8 31.1 185 96.8 86 190.9 53 322.9.1 484.8 97.7 498.5 239.3c21.3 127.4-84.4 256.8-213.5 260.5z' }), React.createElement('path', { d: 'M204.2 256.3c59.2-48.3 117-98.4 177.5-145.3-31.7 48.7-64.3 96.8-96 145.5-27.2.2-54.4.2-81.5-.2z' }),
          React.createElement('path', { d: 'M254.2 283.2c27.3 0 54.5 0 81.7.4-59.6 48.1-117.2 98.5-177.8 145.3 31.8-48.7 64.4-96.9 96.1-145.7z' })); case 'svg': return React.createElement('g', { strokeWidth: '36', strokeOpacity: '.3' }, React.createElement('tile', null, 'SVG'), React.createElement('path', { d: 'M118.8 241.5c-15.7-15.7-41.2-15.7-57 0s-15.7 41.2 0 57c15.7 15.7 41.2 15.7 57 0h302.5c15.7 15.7 41.2 15.7 57 0 15.7-15.7 15.7-41.2 0-57s-41.2-15.7-57 0H118.8z' }), React.createElement('path', { d: 'M183.2 142.9c0-22.2-18-40.3-40.3-40.3-22.2 0-40.3 18-40.3 40.3 0 22.2 18 40.3 40.3 40.3l213.9 213.9c0 22.2 18 40.3 40.3 40.3 22.2 0 40.3-18 40.3-40.3 0-22.2-18-40.3-40.3-40.3L183.2 142.9z' }),
          React.createElement('path', { d: 'M298.5 118.8c15.7-15.7 15.7-41.2 0-57-15.7-15.7-41.2-15.7-57 0-15.7 15.7-15.7 41.2 0 57v302.5c-15.7 15.7-15.7 41.2 0 57 15.7 15.7 41.2 15.7 57 0 15.7-15.7 15.7-41.2 0-57V118.8z' }), React.createElement('path', { d: 'M397.1 183.2c22.2 0 40.3-18 40.3-40.3 0-22.2-18-40.3-40.3-40.3-22.2 0-40.3 18-40.3 40.3L142.9 356.8c-22.2 0-40.3 18-40.3 40.3 0 22.2 18 40.3 40.3 40.3 22.2 0 40.3-18 40.3-40.3l213.9-213.9z' })); case 'swift': return React.createElement('g', null, React.createElement('title',
          null, 'Swift'), React.createElement('path', { d: 'M362.4 422.6c-54 31.2-128.3 34.4-203 2.4-60.5-25.7-110.7-70.8-142.8-122.2C32 315.6 50 325.9 69.3 334.9c77.1 36.2 154.3 33.7 208.6.1l-.1-.1C200.6 275.7 134.9 198.5 86 135.5c-10.3-10.3-18-23.2-25.7-34.7 59.2 54 153.1 122.2 186.6 141.6C176.1 167.7 113.1 75 115.6 77.6c112 113.2 216.2 177.6 216.2 177.6 3.4 1.9 6.1 3.6 8.3 5 2.3-5.7 4.2-11.7 5.9-17.9 18-65.6-2.6-140.3-47.6-202 104.2 63.1 166 181.4 140.3 280.5-.7 2.7-1.4 5.3-2.2 7.9.3.4.6.7.9 1.1 51.5 64.3 37.3 132.5 30.9 119.7-28-54.6-79.7-37.9-105.9-26.9z' }));
        case 'ubuntu': return React.createElement('g', null, React.createElement('title', null, 'Ubuntu'), React.createElement('path', { d: 'M44.4 225.7C19.9 225.7.1 245.5.1 270c0 24.5 19.8 44.3 44.3 44.3 24.5 0 44.3-19.8 44.3-44.3 0-24.5-19.8-44.3-44.3-44.3zM360.7 427c-21.2 12.2-28.4 39.3-16.2 60.5 12.2 21.2 39.3 28.4 60.5 16.2 21.2-12.2 28.4-39.3 16.2-60.5-12.3-21.1-39.4-28.4-60.5-16.2zM140.6 270c0-43.8 21.8-82.4 55-105.9l-32.4-54.2c-38.8 25.9-67.6 65.5-79.6 111.9 14 11.4 22.9 28.8 22.9 48.2 0 19.5-8.9 36.8-22.9 48.2 12 46.4 40.8 86 79.6 111.9l32.4-54.3c-33.2-23.4-55-62-55-105.8zM270 140.6c67.6 0 123 51.8 128.9 117.9l63.1-.9c-3.1-48.8-24.4-92.6-57.2-124.8-16.8 6.4-36.3 5.4-53.1-4.3-16.8-9.7-27.4-26.1-30.3-43.9-16.4-4.5-33.6-7-51.4-7-30.6 0-59.6 7.2-85.3 19.9l30.8 55.2c16.6-7.8 35-12.1 54.5-12.1zm0 258.8c-19.5 0-37.9-4.3-54.5-12l-30.8 55.2c25.7 12.7 54.7 19.9 85.3 19.9 17.8 0 35-2.5 51.4-7 2.9-17.8 13.5-34.2 30.3-43.9 16.8-9.7 36.3-10.7 53.1-4.3 32.8-32.2 54.1-76 57.2-124.8l-63.1-.9c-5.8 66-61.3 117.8-128.9 117.8zM360.7 113c21.2 12.2 48.3 5 60.5-16.2 12.2-21.2 5-48.3-16.2-60.5-21.2-12.2-48.3-5-60.5 16.2-12.3 21.2-5 48.2 16.2 60.5z' }));
        case 'webpack': return React.createElement('g', null, React.createElement('title', null, 'Webpack'), React.createElement('path', { opacity: '.3', d: 'M270.1.8L36.6 135.6v269.6L270.1 540l233.4-134.8V135.6z' }), React.createElement('path', { opacity: '.6', d: 'M270.1 137.6l-115.8 66.8v133.7l115.8 66.8 115.7-66.8V204.4z' }), React.createElement('path', { opacity: '.3', d: 'M270 0L36 135.7l234 127.4 234-126.6z' }), React.createElement('path', { opacity: '.3', d: 'M270 280.6l-234 124 234 134.9 233.1-135.7z' })); default: console.error(`Icon "${
          a}" not matched`);
      }
    },
  }]); return a;
}(Component)), _class.propTypes = { name: PropTypes.string.isRequired }, _class.defaultProps = { height: '1em' }, _temp); const Logo = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      return React.createElement('a', { href: '/', style: { pointerEvents: 'auto', margin: '0 0 40px' }, ...this.props }, React.createElement('img', {
        src: content.logo.images.main,
        id: 'logo',
        height: '42px',
      }));
    },
  }]); return a;
}(Component)); const SceneWrapper = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{ key: 'componentDidMount', value() { this.parallax = new Parallax(this.refs.scene); } }, {
    key: 'render',
    value() {
      const a = { ...styles.full, ..._defineProperty({ overflow: 'hidden', color: styles.colors.textDefault, background: 'red' }, 'background', 'linear-gradient(145deg, #dde0e4, #717f96)') };
      const b = { ...styles.full, position: 'relative' }; return React.createElement('div', { ref: 'scene', style: a }, React.createElement('div', { className: 'layer', 'data-depth': '4', style: b }, this.props.children));
    },
  }]); return a;
}(Component)); const Books = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.center,
        height: '80%',
        transform: 'translate3d(0, -100%, 0)',
        textAlign: 'left',
      }; return React.createElement('div', { style: a }, React.createElement('img', { src: content.books.images.main, height: '100%', style: { transform: 'transform: translate3d(10%, 0, 0)' } }));
    },
  }]); return a;
}(Component)); const DeviceTextbox = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.flex,
        textAlign: 'left',
        opacity: '0.6',
        ...this.props.style,
      }; const b = { ...styles.headline, letterSpacing: '0.2vh' }; const d = this.props.content; const f = d.paragraph; const g = d.icons; return React.createElement('div', { style: a }, React.createElement('h3', { style: b }, d.title), React.createElement('p', { style: styles.text }, f), React.createElement('div', { style: { justifyContent: 'flex-start', fontSize: '3vh' } }, g.map((a) => React.createElement(Icon, { key: a, name: a }))));
    },
  }]); return a;
}(Component)); const DeviceScreen = (function (b) {
  function a() {
    _classCallCheck(this, a); return _possibleConstructorReturn(this,
      (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments));
  }_inherits(a, b); _createClass(a, [{
    key: 'handleStyle',
    value(a) {
      return {
        position: 'absolute', top: '0%', left: '0%', height: '100%', width: '100%', background: `\n        linear-gradient(${this.props.angle},\n          rgba(255, 255, 255, 0.${1 + 1 * a}),\n          rgba(255, 255, 255, 0.05)\n        )\n      `,
      };
    },
  }, {
    key: 'render',
    value() {
      const a = this; return React.createElement('div', { style: this.props.style }, this.props.children.map((b,
        c) => React.createElement('div', {
        key: c, className: 'layer', 'data-depth': 2 * c / 10, style: a.handleStyle(c),
      }, b)));
    },
  }]); return a;
}(Component)); const Ipad = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.center, height: '50%', transform: 'translate3d(-100%, 0, 0)', textAlign: 'right',
      }; const b = {
        position: 'absolute',
        top: '0%',
        right: '0%',
        width: `${768
/ 1042 * 100}%`,
        height: `${1024 / 532 * 100}%`,
        transform: '\n        translate3d(-26.5%, 22.25%, 0)\n        rotate3d(0,0,1, -45deg)\n        skew(18.5deg, 18.5deg)\n        scale3d(0.518, 0.496, 1)\n      ',
        transformOrigin: '0 0',
        background: 'linear-gradient(\n        200deg,\n        #c1d0db,\n        #a1a9bf\n      )',
      }; return React.createElement('div', { style: a }, React.createElement('img', { src: content.ipad.images.main, height: '100%', style: { transform: 'transform: translate3d(0, 0, 0)' } }), React.createElement(DeviceScreen,
        { angle: '240deg', style: b }, content.ipad.images.screens.map((a) => React.createElement('img', { src: a, height: '100%' }))), React.createElement(DeviceTextbox, {
        content: content.ipad,
        style: {
          position: 'absolute', top: '0%', right: '0%', width: '22%', textAlign: 'left', transform: '\n        translate3d(-110%, -40%, 0)\n        rotate3d(0,0,1, -45deg)\n        skew(18.5deg, 18.5deg)\n        scale3d(0.85, 1, 1)\n      ',
        },
      }));
    },
  }]); return a;
}(Component)); const Iphone = (function (b) {
  function a() {
    _classCallCheck(this, a); return _possibleConstructorReturn(this,
      (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments));
  }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.center, height: '50%', transform: 'translate3d(-100%, -100%, 0)', textAlign: 'right',
      }; const b = {
        position: 'absolute', top: '0%', right: '0%', width: `${375 / 867 * 100}%`, height: `${670 / 667 * 100}%`, transform: '\n        skew(0deg, -26.5deg)\n        translate3d(-4.7%, 18.2%, 0)\n        scale3d(0.406, 0.504, 1)\n      ', transformOrigin: '0 0', background: 'linear-gradient(\n        180deg,\n        #abbcc8,\n        #7783a2\n      )',
      };
      return React.createElement('div', { style: a }, React.createElement('img', { src: content.iphone.images.secondary, height: '100%', style: { position: 'absolute', transform: 'translate3d(-27%,0,0)' } }), React.createElement('img', { src: content.iphone.images.main, height: '100%', style: { transform: 'translate3d(50%, 0,0)' } }), React.createElement(DeviceScreen, { angle: '180deg', style: b }, content.iphone.images.screens.map((a) => React.createElement('img', { src: a, height: '100%' }))), React.createElement(DeviceTextbox, {
        content: content.iphone,
        style: {
          position: 'absolute', top: '0%', right: '0%', width: '30%', textAlign: 'left', transform: '\n        skew(0deg, -26.5deg)\n        translate3d(5%, -5%, 0)\n        scale3d(0.8, 1, 1)\n      ',
        },
      }));
    },
  }]); return a;
}(Component)); const Macbook = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.center,
        height: '120%',
        transform: 'translate3d(-10%, -30%,0)',
        textAlign: 'left',
      }; const b = {
        position: 'absolute', top: '0%', left: '0%', width: `${1440 / 2231 * 100}%`, height: `${900 / 1456 * 100}%`, transform: '\n        translate3d(49.5%, 7%, 0)\n        skew(-10.5deg, 26deg)\n        scale3d(0.523, 0.677, 1)\n      ', transformOrigin: '0 0', background: 'linear-gradient(\n        140deg,\n        #c1d0db,\n        #7783a2\n      )',
      }; return React.createElement('div', { style: a }, React.createElement('img', { src: content.macbook.images.main, height: '100%', style: { transform: 'translate3d(0, 0, 0)' } }),
        React.createElement(DeviceScreen, { angle: '110deg', style: b }, content.macbook.images.screens.map((a) => React.createElement('img', { src: a, height: '100%' }))), React.createElement(DeviceTextbox, {
          content: content.macbook,
          style: {
            position: 'absolute', top: '0%', left: '0%', width: '15%', textAlign: 'left', transform: '\n        translate3d(227%, -110%, 0)\n        skew(-10.5deg, 26deg)\n        scale3d(0.9, 1, 1)\n      ',
          },
        }));
    },
  }]); return a;
}(Component)); const SceneBackground = (function (b) {
  function a() {
    _classCallCheck(this,
      a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments));
  }_inherits(a, b); _createClass(a, [{ key: 'render', value() { return React.createElement('span', null, this.props.children); } }]); return a;
}(Component)); const SceneForeground = (function (b) {
  function a() { _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments)); }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.full,
        position: 'absolute',
        flexDirection: 'column',
        justifyContent: 'center',
        userSelect: 'none',
        pointerEvents: 'none',
      }; const b = {
        ...styles.full, ...styles.flex, flexDirection: 'column', justifyContent: 'center', alignItems: 'center',
      }; return React.createElement('div', { className: 'layer', 'data-depth': '0.2', style: a }, React.createElement('div', { style: b }, this.props.children));
    },
  }]); return a;
}(Component)); const Hero = (function (b) {
  function a() {
    _classCallCheck(this, a); return _possibleConstructorReturn(this, (a.__proto__ || Object.getPrototypeOf(a)).apply(this,
      arguments));
  }_inherits(a, b); _createClass(a, [{
    key: 'render',
    value() {
      const a = {
        ...styles.flex, justifyContent: 'center', alignItems: 'center', pointerEvents: 'auto',
      }; const b = { ...styles.headline, fontSize: '4vh' }; const d = { ...styles.headline, fontSize: '2vh' }; return React.createElement('div', { style: a }, React.createElement('h1', { style: b }, content.title), React.createElement('h2', { style: d }, content.subtitle));
    },
  }]); return a;
}(Component)); const App = (function (b) {
  function a() {
    _classCallCheck(this, a); return _possibleConstructorReturn(this,
      (a.__proto__ || Object.getPrototypeOf(a)).apply(this, arguments));
  }_inherits(a, b); _createClass(a, [{ key: 'render', value() { return React.createElement(SceneWrapper, null, React.createElement(SceneBackground, null, React.createElement(Books, null), React.createElement(Macbook, null), React.createElement(Ipad, null), React.createElement(Iphone, null)), React.createElement(SceneForeground, null, React.createElement(Logo, null), React.createElement(Hero, null))); } }]); return a;
}(Component));
render(React.createElement(App, null), document.getElementById('app'));
