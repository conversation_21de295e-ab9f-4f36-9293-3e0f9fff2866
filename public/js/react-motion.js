(function webpackUniversalModuleDefinition(root, factory) {
  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory(require('react'));
  else if (typeof define === 'function' && define.amd) define(['react'], factory);
  else if (typeof exports === 'object') exports.ReactMotion = factory(require('react'));
  else root.ReactMotion = factory(root.React);
}(this, (__WEBPACK_EXTERNAL_MODULE_9__) =>
/** *** */ (function (modules) { // webpackBootstrap
    /** *** */ 	// The module cache
    /** *** */ 	const installedModules = {};
    /** *** */
    /** *** */ 	// The require function
    /** *** */ 	function __webpack_require__(moduleId) {
      /** *** */
      /** *** */ 		// Check if module is in cache
      /** *** */ 		if (installedModules[moduleId])
      /** *** */ 			{ return installedModules[moduleId].exports; }
      /** *** */
      /** *** */ 		// Create a new module (and put it into the cache)
      /** *** */ 		const module = installedModules[moduleId] = {
        /** *** */ 			exports: {},
        /** *** */ 			id: moduleId,
        /** *** */ 			loaded: false,
        /** *** */ 		};
      /** *** */
      /** *** */ 		// Execute the module function
      /** *** */ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
      /** *** */
      /** *** */ 		// Flag the module as loaded
      /** *** */ 		module.loaded = true;
      /** *** */
      /** *** */ 		// Return the exports of the module
      /** *** */ 		return module.exports;
      /** *** */ 	}
    /** *** */
    /** *** */
    /** *** */ 	// expose the modules object (__webpack_modules__)
    /** *** */ 	__webpack_require__.m = modules;
    /** *** */
    /** *** */ 	// expose the module cache
    /** *** */ 	__webpack_require__.c = installedModules;
    /** *** */
    /** *** */ 	// __webpack_public_path__
    /** *** */ 	__webpack_require__.p = 'build/';
    /** *** */
    /** *** */ 	// Load entry module and return exports
    /** *** */ 	return __webpack_require__(0);
    /** *** */ }([
    /* 0 */
    /***/ function (module, exports, __webpack_require__) {
      exports.__esModule = true;

      function _interopRequire(obj) { return obj && obj.__esModule ? obj.default : obj; }

      const _Motion = __webpack_require__(1);

      exports.Motion = _interopRequire(_Motion);

      const _StaggeredMotion = __webpack_require__(10);

      exports.StaggeredMotion = _interopRequire(_StaggeredMotion);

      const _TransitionMotion = __webpack_require__(11);

      exports.TransitionMotion = _interopRequire(_TransitionMotion);

      const _spring = __webpack_require__(13);

      exports.spring = _interopRequire(_spring);

      const _presets = __webpack_require__(14);

      exports.presets = _interopRequire(_presets);

      // deprecated, dummy warning function

      const _reorderKeys = __webpack_require__(15);

      exports.reorderKeys = _interopRequire(_reorderKeys);
      /***/ },
    /* 1 */
    /***/ function (module, exports, __webpack_require__) {
      exports.__esModule = true;

      const _extends = Object.assign || function (target) { for (let i = 1; i < arguments.length; i++) { const source = arguments[i]; for (const key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

      function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

      const _mapToZero = __webpack_require__(2);

      const _mapToZero2 = _interopRequireDefault(_mapToZero);

      const _stripStyle = __webpack_require__(3);

      const _stripStyle2 = _interopRequireDefault(_stripStyle);

      const _stepper3 = __webpack_require__(4);

      const _stepper4 = _interopRequireDefault(_stepper3);

      const _performanceNow = __webpack_require__(5);

      const _performanceNow2 = _interopRequireDefault(_performanceNow);

      const _raf = __webpack_require__(7);

      const _raf2 = _interopRequireDefault(_raf);

      const _shouldStopAnimation = __webpack_require__(8);

      const _shouldStopAnimation2 = _interopRequireDefault(_shouldStopAnimation);

      const _react = __webpack_require__(9);

      const _react2 = _interopRequireDefault(_react);

      const msPerFrame = 1000 / 60;

      const Motion = _react2.default.createClass({
	  displayName: 'Motion',

	  propTypes: {
	    // TOOD: warn against putting a config in here
	    defaultStyle: _react.PropTypes.objectOf(_react.PropTypes.number),
	    style: _react.PropTypes.objectOf(_react.PropTypes.oneOfType([_react.PropTypes.number, _react.PropTypes.object])).isRequired,
	    children: _react.PropTypes.func.isRequired,
	    onRest: _react.PropTypes.func,
	  },

	  getInitialState: function getInitialState() {
	    const _props = this.props;
	    const { defaultStyle } = _props;
	    const { style } = _props;

	    const currentStyle = defaultStyle || _stripStyle2.default(style);
	    const currentVelocity = _mapToZero2.default(currentStyle);
	    return {
	      currentStyle,
	      currentVelocity,
	      lastIdealStyle: currentStyle,
	      lastIdealVelocity: currentVelocity,
	    };
	  },

	  wasAnimating: false,
	  animationID: null,
	  prevTime: 0,
	  accumulatedTime: 0,
	  // it's possible that currentStyle's value is stale: if props is immediately
	  // changed from 0 to 400 to spring(0) again, the async currentStyle is still
	  // at 0 (didn't have time to tick and interpolate even once). If we naively
	  // compare currentStyle with destVal it'll be 0 === 0 (no animation, stop).
	  // In reality currentStyle should be 400
	  unreadPropStyle: null,
	  // after checking for unreadPropStyle != null, we manually go set the
	  // non-interpolating values (those that are a number, without a spring
	  // config)
	  clearUnreadPropStyle: function clearUnreadPropStyle(destStyle) {
	    let dirty = false;
	    const _state = this.state;
	    let { currentStyle } = _state;
	    let { currentVelocity } = _state;
	    let { lastIdealStyle } = _state;
	    let { lastIdealVelocity } = _state;

	    for (const key in destStyle) {
	      if (!destStyle.hasOwnProperty(key)) {
	        continue;
	      }

	      const styleValue = destStyle[key];
	      if (typeof styleValue === 'number') {
	        if (!dirty) {
	          dirty = true;
	          currentStyle = { ...currentStyle };
	          currentVelocity = { ...currentVelocity };
	          lastIdealStyle = { ...lastIdealStyle };
	          lastIdealVelocity = { ...lastIdealVelocity };
	        }

	        currentStyle[key] = styleValue;
	        currentVelocity[key] = 0;
	        lastIdealStyle[key] = styleValue;
	        lastIdealVelocity[key] = 0;
	      }
	    }

	    if (dirty) {
	      this.setState({
              currentStyle, currentVelocity, lastIdealStyle, lastIdealVelocity,
            });
	    }
	  },

	  startAnimationIfNecessary: function startAnimationIfNecessary() {
	    const _this = this;

	    // TODO: when config is {a: 10} and dest is {a: 10} do we raf once and
	    // call cb? No, otherwise accidental parent rerender causes cb trigger
	    this.animationID = _raf2.default(() => {
	      // check if we need to animate in the first place
	      const propsStyle = _this.props.style;
	      if (_shouldStopAnimation2.default(_this.state.currentStyle, propsStyle, _this.state.currentVelocity)) {
	        if (_this.wasAnimating && _this.props.onRest) {
	          _this.props.onRest();
	        }

	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.wasAnimating = false;
	        _this.accumulatedTime = 0;
	        return;
	      }

	      _this.wasAnimating = true;

	      const currentTime = _performanceNow2.default();
	      const timeDelta = currentTime - _this.prevTime;
	      _this.prevTime = currentTime;
	      _this.accumulatedTime += timeDelta;
	      // more than 10 frames? prolly switched browser tab. Restart
	      if (_this.accumulatedTime > msPerFrame * 10) {
	        _this.accumulatedTime = 0;
	      }

	      if (_this.accumulatedTime === 0) {
	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.startAnimationIfNecessary();
	        return;
	      }

	      const currentFrameCompletion = (_this.accumulatedTime - Math.floor(_this.accumulatedTime / msPerFrame) * msPerFrame) / msPerFrame;
	      const framesToCatchUp = Math.floor(_this.accumulatedTime / msPerFrame);

	      const newLastIdealStyle = {};
	      const newLastIdealVelocity = {};
	      const newCurrentStyle = {};
	      const newCurrentVelocity = {};

	      for (const key in propsStyle) {
	        if (!propsStyle.hasOwnProperty(key)) {
	          continue;
	        }

	        const styleValue = propsStyle[key];
	        if (typeof styleValue === 'number') {
	          newCurrentStyle[key] = styleValue;
	          newCurrentVelocity[key] = 0;
	          newLastIdealStyle[key] = styleValue;
	          newLastIdealVelocity[key] = 0;
	        } else {
	          let newLastIdealStyleValue = _this.state.lastIdealStyle[key];
	          let newLastIdealVelocityValue = _this.state.lastIdealVelocity[key];
	          for (let i = 0; i < framesToCatchUp; i++) {
	            const _stepper = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	            newLastIdealStyleValue = _stepper[0];
	            newLastIdealVelocityValue = _stepper[1];
	          }

	          const _stepper2 = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	          const nextIdealX = _stepper2[0];
	          const nextIdealV = _stepper2[1];

	          newCurrentStyle[key] = newLastIdealStyleValue + (nextIdealX - newLastIdealStyleValue) * currentFrameCompletion;
	          newCurrentVelocity[key] = newLastIdealVelocityValue + (nextIdealV - newLastIdealVelocityValue) * currentFrameCompletion;
	          newLastIdealStyle[key] = newLastIdealStyleValue;
	          newLastIdealVelocity[key] = newLastIdealVelocityValue;
	        }
	      }

	      _this.animationID = null;
	      // the amount we're looped over above
	      _this.accumulatedTime -= framesToCatchUp * msPerFrame;

	      _this.setState({
	        currentStyle: newCurrentStyle,
	        currentVelocity: newCurrentVelocity,
	        lastIdealStyle: newLastIdealStyle,
	        lastIdealVelocity: newLastIdealVelocity,
	      });

	      _this.unreadPropStyle = null;

	      _this.startAnimationIfNecessary();
	    });
	  },

	  componentDidMount: function componentDidMount() {
	    this.prevTime = _performanceNow2.default();
	    this.startAnimationIfNecessary();
	  },

	  componentWillReceiveProps: function componentWillReceiveProps(props) {
	    if (this.unreadPropStyle != null) {
	      // previous props haven't had the chance to be set yet; set them here
	      this.clearUnreadPropStyle(this.unreadPropStyle);
	    }

	    this.unreadPropStyle = props.style;
	    if (this.animationID == null) {
	      this.prevTime = _performanceNow2.default();
	      this.startAnimationIfNecessary();
	    }
	  },

	  componentWillUnmount: function componentWillUnmount() {
	    if (this.animationID != null) {
	      _raf2.default.cancel(this.animationID);
	      this.animationID = null;
	    }
	  },

	  render: function render() {
	    const renderedChildren = this.props.children(this.state.currentStyle);
	    return renderedChildren && _react2.default.Children.only(renderedChildren);
	  },
      });

      exports.default = Motion;
      module.exports = exports.default;
      /***/ },
    /* 2 */
    /***/ function (module, exports) {
      // currently used to initiate the velocity style object to 0

      exports.__esModule = true;
      exports.default = mapToZero;

      function mapToZero(obj) {
	  const ret = {};
	  for (const key in obj) {
	    if (obj.hasOwnProperty(key)) {
	      ret[key] = 0;
	    }
	  }
	  return ret;
      }

      module.exports = exports.default;
      /***/ },
    /* 3 */
    /***/ function (module, exports) {
      // turn {x: {val: 1, stiffness: 1, damping: 2}, y: 2} generated by
      // `{x: spring(1, {stiffness: 1, damping: 2}), y: 2}` into {x: 1, y: 2}

      exports.__esModule = true;
      exports.default = stripStyle;

      function stripStyle(style) {
	  const ret = {};
	  for (const key in style) {
	    if (!style.hasOwnProperty(key)) {
	      continue;
	    }
	    ret[key] = typeof style[key] === 'number' ? style[key] : style[key].val;
	  }
	  return ret;
      }

      module.exports = exports.default;
      /***/ },
    /* 4 */
    /***/ function (module, exports) {
      // stepper is used a lot. Saves allocation to return the same array wrapper.
      // This is fine and danger-free against mutations because the callsite
      // immediately destructures it and gets the numbers inside without passing the

      exports.__esModule = true;
      exports.default = stepper;

      const reusedTuple = [];

      function stepper(secondPerFrame, x, v, destX, k, b, precision) {
	  // Spring stiffness, in kg / s^2

	  // for animations, destX is really spring length (spring at rest). initial
	  // position is considered as the stretched/compressed position of a spring
	  const Fspring = -k * (x - destX);

	  // Damping, in kg / s
	  const Fdamper = -b * v;

	  // usually we put mass here, but for animation purposes, specifying mass is a
	  // bit redundant. you could simply adjust k and b accordingly
	  // let a = (Fspring + Fdamper) / mass;
	  const a = Fspring + Fdamper;

	  const newV = v + a * secondPerFrame;
	  const newX = x + newV * secondPerFrame;

	  if (Math.abs(newV) < precision && Math.abs(newX - destX) < precision) {
	    reusedTuple[0] = destX;
	    reusedTuple[1] = 0;
	    return reusedTuple;
	  }

	  reusedTuple[0] = newX;
	  reusedTuple[1] = newV;
	  return reusedTuple;
      }

      module.exports = exports.default;
      // array reference around.
      /***/ },
    /* 5 */
    /***/ function (module, exports, __webpack_require__) {
      /* WEBPACK VAR INJECTION */(function (process) { // Generated by CoffeeScript 1.7.1

"use strict";

        (function () {
	  let getNanoSeconds; let hrtime; let
            loadTime;

	  if (typeof performance !== 'undefined' && performance !== null && performance.now) {
	    module.exports = function () {
	      return performance.now();
	    };
	  } else if (typeof process !== 'undefined' && process !== null && process.hrtime) {
	    module.exports = function () {
	      return (getNanoSeconds() - loadTime) / 1e6;
	    };
	    hrtime = process.hrtime;
	    getNanoSeconds = function () {
	      let hr;
	      hr = hrtime();
	      return hr[0] * 1e9 + hr[1];
	    };
	    loadTime = getNanoSeconds();
	  } else if (Date.now) {
	    module.exports = function () {
	      return Date.now() - loadTime;
	    };
	    loadTime = Date.now();
	  } else {
	    module.exports = function () {
	      return new Date().getTime() - loadTime;
	    };
	    loadTime = new Date().getTime();
	  }
        }).call(undefined);
        /* WEBPACK VAR INJECTION */ }.call(exports, __webpack_require__(6)));
      /***/ },
    /* 6 */
    /***/ function (module, exports) {
      // shim for using process in browser

      const process = module.exports = {};
      let queue = [];
      let draining = false;
      let currentQueue;
      let queueIndex = -1;

      function cleanUpNextTick() {
	    draining = false;
	    if (currentQueue.length) {
	        queue = currentQueue.concat(queue);
	    } else {
	        queueIndex = -1;
	    }
	    if (queue.length) {
	        drainQueue();
	    }
      }

      function drainQueue() {
	    if (draining) {
	        return;
	    }
	    const timeout = setTimeout(cleanUpNextTick);
	    draining = true;

	    let len = queue.length;
	    while (len) {
	        currentQueue = queue;
	        queue = [];
	        while (++queueIndex < len) {
	            if (currentQueue) {
	                currentQueue[queueIndex].run();
	            }
	        }
	        queueIndex = -1;
	        len = queue.length;
	    }
	    currentQueue = null;
	    draining = false;
	    clearTimeout(timeout);
      }

      process.nextTick = function (fun) {
	    const args = new Array(arguments.length - 1);
	    if (arguments.length > 1) {
	        for (let i = 1; i < arguments.length; i++) {
	            args[i - 1] = arguments[i];
	        }
	    }
	    queue.push(new Item(fun, args));
	    if (queue.length === 1 && !draining) {
	        setTimeout(drainQueue, 0);
	    }
      };

      // v8 likes predictible objects
      function Item(fun, array) {
	    this.fun = fun;
	    this.array = array;
      }
      Item.prototype.run = function () {
	    this.fun.apply(null, this.array);
      };
      process.title = 'browser';
      process.browser = true;
      process.env = {};
      process.argv = [];
      process.version = ''; // empty string to avoid regexp issues
      process.versions = {};

      function noop() {}

      process.on = noop;
      process.addListener = noop;
      process.once = noop;
      process.off = noop;
      process.removeListener = noop;
      process.removeAllListeners = noop;
      process.emit = noop;

      process.binding = function (name) {
	    throw new Error('process.binding is not supported');
      };

      process.cwd = function () {
	    return '/';
      };
      process.chdir = function (dir) {
	    throw new Error('process.chdir is not supported');
      };
      process.umask = function () {
	    return 0;
      };
      /***/ },
    /* 7 */
    /***/ function (module, exports, __webpack_require__) {
      /* WEBPACK VAR INJECTION */(function (global) {
        const now = __webpack_require__(5);
	    const root = typeof window === 'undefined' ? global : window;
	    const vendors = ['moz', 'webkit'];
	    const suffix = 'AnimationFrame';
	    let raf = root[`request${suffix}`];
	    let caf = root[`cancel${suffix}`] || root[`cancelRequest${suffix}`];

        for (let i = 0; !raf && i < vendors.length; i++) {
	  raf = root[`${vendors[i]}Request${suffix}`];
	  caf = root[`${vendors[i]}Cancel${suffix}`] || root[`${vendors[i]}CancelRequest${suffix}`];
        }

        // Some versions of FF have rAF but not cAF
        if (!raf || !caf) {
	  let last = 0;
	      let id = 0;
	      const queue = [];
	      const frameDuration = 1000 / 60;

	  raf = function (callback) {
	    if (queue.length === 0) {
	      const _now = now();
	          const next = Math.max(0, frameDuration - (_now - last));
	      last = next + _now;
	      setTimeout(() => {
	        const cp = queue.slice(0);
	        // Clear queue here to prevent
	        // callbacks from appending listeners
	        // to the current frame's queue
	        queue.length = 0;
	        for (let i = 0; i < cp.length; i++) {
	          if (!cp[i].cancelled) {
	            try {
	              cp[i].callback(last);
	            } catch (e) {
	              setTimeout(() => {
	                throw e;
	              }, 0);
	            }
	          }
	        }
	      }, Math.round(next));
	    }
	    queue.push({
	      handle: ++id,
	      callback,
	      cancelled: false,
	    });
	    return id;
	  };

	  caf = function (handle) {
	    for (let i = 0; i < queue.length; i++) {
	      if (queue[i].handle === handle) {
	        queue[i].cancelled = true;
	      }
	    }
	  };
        }

        module.exports = function (fn) {
	  // Wrap in a new function to prevent
	  // `cancel` potentially being assigned
	  // to the native rAF function
	  return raf.call(root, fn);
        };
        module.exports.cancel = function () {
	  caf.apply(root, arguments);
        };
        module.exports.polyfill = function () {
	  root.requestAnimationFrame = raf;
	  root.cancelAnimationFrame = caf;
        };
        /* WEBPACK VAR INJECTION */ }.call(exports, (function () { return this; }())));
      /***/ },
    /* 8 */
    /***/ function (module, exports) {
      // usage assumption: currentStyle values have already been rendered but it says
      // nothing of whether currentStyle is stale (see unreadPropStyle)

      exports.__esModule = true;
      exports.default = shouldStopAnimation;

      function shouldStopAnimation(currentStyle, style, currentVelocity) {
	  for (const key in style) {
	    if (!style.hasOwnProperty(key)) {
	      continue;
	    }

	    if (currentVelocity[key] !== 0) {
	      return false;
	    }

	    const styleValue = typeof style[key] === 'number' ? style[key] : style[key].val;
	    // stepper will have already taken care of rounding precision errors, so
	    // won't have such thing as 0.9999 !=== 1
	    if (currentStyle[key] !== styleValue) {
	      return false;
	    }
	  }

	  return true;
      }

      module.exports = exports.default;
      /***/ },
    /* 9 */
    /***/ function (module, exports) {
      module.exports = __WEBPACK_EXTERNAL_MODULE_9__;
      /***/ },
    /* 10 */
    /***/ function (module, exports, __webpack_require__) {
      exports.__esModule = true;

      const _extends = Object.assign || function (target) { for (let i = 1; i < arguments.length; i++) { const source = arguments[i]; for (const key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

      function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

      const _mapToZero = __webpack_require__(2);

      const _mapToZero2 = _interopRequireDefault(_mapToZero);

      const _stripStyle = __webpack_require__(3);

      const _stripStyle2 = _interopRequireDefault(_stripStyle);

      const _stepper3 = __webpack_require__(4);

      const _stepper4 = _interopRequireDefault(_stepper3);

      const _performanceNow = __webpack_require__(5);

      const _performanceNow2 = _interopRequireDefault(_performanceNow);

      const _raf = __webpack_require__(7);

      const _raf2 = _interopRequireDefault(_raf);

      const _shouldStopAnimation = __webpack_require__(8);

      const _shouldStopAnimation2 = _interopRequireDefault(_shouldStopAnimation);

      const _react = __webpack_require__(9);

      const _react2 = _interopRequireDefault(_react);

      const msPerFrame = 1000 / 60;

      function shouldStopAnimationAll(currentStyles, styles, currentVelocities) {
	  for (let i = 0; i < currentStyles.length; i++) {
	    if (!_shouldStopAnimation2.default(currentStyles[i], styles[i], currentVelocities[i])) {
	      return false;
	    }
	  }
	  return true;
      }

      const StaggeredMotion = _react2.default.createClass({
	  displayName: 'StaggeredMotion',

	  propTypes: {
	    // TOOD: warn against putting a config in here
	    defaultStyles: _react.PropTypes.arrayOf(_react.PropTypes.objectOf(_react.PropTypes.number)),
	    styles: _react.PropTypes.func.isRequired,
	    children: _react.PropTypes.func.isRequired,
	  },

	  getInitialState: function getInitialState() {
	    const _props = this.props;
	    const { defaultStyles } = _props;
	    const { styles } = _props;

	    const currentStyles = defaultStyles || styles().map(_stripStyle2.default);
	    const currentVelocities = currentStyles.map((currentStyle) => _mapToZero2.default(currentStyle));
	    return {
	      currentStyles,
	      currentVelocities,
	      lastIdealStyles: currentStyles,
	      lastIdealVelocities: currentVelocities,
	    };
	  },

	  animationID: null,
	  prevTime: 0,
	  accumulatedTime: 0,
	  // it's possible that currentStyle's value is stale: if props is immediately
	  // changed from 0 to 400 to spring(0) again, the async currentStyle is still
	  // at 0 (didn't have time to tick and interpolate even once). If we naively
	  // compare currentStyle with destVal it'll be 0 === 0 (no animation, stop).
	  // In reality currentStyle should be 400
	  unreadPropStyles: null,
	  // after checking for unreadPropStyles != null, we manually go set the
	  // non-interpolating values (those that are a number, without a spring
	  // config)
	  clearUnreadPropStyle: function clearUnreadPropStyle(unreadPropStyles) {
	    const _state = this.state;
	    const { currentStyles } = _state;
	    const { currentVelocities } = _state;
	    const { lastIdealStyles } = _state;
	    const { lastIdealVelocities } = _state;

	    let someDirty = false;
	    for (let i = 0; i < unreadPropStyles.length; i++) {
	      const unreadPropStyle = unreadPropStyles[i];
	      let dirty = false;

	      for (const key in unreadPropStyle) {
	        if (!unreadPropStyle.hasOwnProperty(key)) {
	          continue;
	        }

	        const styleValue = unreadPropStyle[key];
	        if (typeof styleValue === 'number') {
	          if (!dirty) {
	            dirty = true;
	            someDirty = true;
	            currentStyles[i] = { ...currentStyles[i] };
	            currentVelocities[i] = { ...currentVelocities[i] };
	            lastIdealStyles[i] = { ...lastIdealStyles[i] };
	            lastIdealVelocities[i] = { ...lastIdealVelocities[i] };
	          }
	          currentStyles[i][key] = styleValue;
	          currentVelocities[i][key] = 0;
	          lastIdealStyles[i][key] = styleValue;
	          lastIdealVelocities[i][key] = 0;
	        }
	      }
	    }

	    if (someDirty) {
	      this.setState({
              currentStyles, currentVelocities, lastIdealStyles, lastIdealVelocities,
            });
	    }
	  },

	  startAnimationIfNecessary: function startAnimationIfNecessary() {
	    const _this = this;

	    // TODO: when config is {a: 10} and dest is {a: 10} do we raf once and
	    // call cb? No, otherwise accidental parent rerender causes cb trigger
	    this.animationID = _raf2.default(() => {
	      const destStyles = _this.props.styles(_this.state.lastIdealStyles);

	      // check if we need to animate in the first place
	      if (shouldStopAnimationAll(_this.state.currentStyles, destStyles, _this.state.currentVelocities)) {
	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.accumulatedTime = 0;
	        return;
	      }

	      const currentTime = _performanceNow2.default();
	      const timeDelta = currentTime - _this.prevTime;
	      _this.prevTime = currentTime;
	      _this.accumulatedTime += timeDelta;
	      // more than 10 frames? prolly switched browser tab. Restart
	      if (_this.accumulatedTime > msPerFrame * 10) {
	        _this.accumulatedTime = 0;
	      }

	      if (_this.accumulatedTime === 0) {
	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.startAnimationIfNecessary();
	        return;
	      }

	      const currentFrameCompletion = (_this.accumulatedTime - Math.floor(_this.accumulatedTime / msPerFrame) * msPerFrame) / msPerFrame;
	      const framesToCatchUp = Math.floor(_this.accumulatedTime / msPerFrame);

	      const newLastIdealStyles = [];
	      const newLastIdealVelocities = [];
	      const newCurrentStyles = [];
	      const newCurrentVelocities = [];

	      for (let i = 0; i < destStyles.length; i++) {
	        const destStyle = destStyles[i];
	        const newCurrentStyle = {};
	        const newCurrentVelocity = {};
	        const newLastIdealStyle = {};
	        const newLastIdealVelocity = {};

	        for (const key in destStyle) {
	          if (!destStyle.hasOwnProperty(key)) {
	            continue;
	          }

	          const styleValue = destStyle[key];
	          if (typeof styleValue === 'number') {
	            newCurrentStyle[key] = styleValue;
	            newCurrentVelocity[key] = 0;
	            newLastIdealStyle[key] = styleValue;
	            newLastIdealVelocity[key] = 0;
	          } else {
	            let newLastIdealStyleValue = _this.state.lastIdealStyles[i][key];
	            let newLastIdealVelocityValue = _this.state.lastIdealVelocities[i][key];
	            for (let j = 0; j < framesToCatchUp; j++) {
	              const _stepper = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	              newLastIdealStyleValue = _stepper[0];
	              newLastIdealVelocityValue = _stepper[1];
	            }

	            const _stepper2 = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	            const nextIdealX = _stepper2[0];
	            const nextIdealV = _stepper2[1];

	            newCurrentStyle[key] = newLastIdealStyleValue + (nextIdealX - newLastIdealStyleValue) * currentFrameCompletion;
	            newCurrentVelocity[key] = newLastIdealVelocityValue + (nextIdealV - newLastIdealVelocityValue) * currentFrameCompletion;
	            newLastIdealStyle[key] = newLastIdealStyleValue;
	            newLastIdealVelocity[key] = newLastIdealVelocityValue;
	          }
	        }

	        newCurrentStyles[i] = newCurrentStyle;
	        newCurrentVelocities[i] = newCurrentVelocity;
	        newLastIdealStyles[i] = newLastIdealStyle;
	        newLastIdealVelocities[i] = newLastIdealVelocity;
	      }

	      _this.animationID = null;
	      // the amount we're looped over above
	      _this.accumulatedTime -= framesToCatchUp * msPerFrame;

	      _this.setState({
	        currentStyles: newCurrentStyles,
	        currentVelocities: newCurrentVelocities,
	        lastIdealStyles: newLastIdealStyles,
	        lastIdealVelocities: newLastIdealVelocities,
	      });

	      _this.unreadPropStyles = null;

	      _this.startAnimationIfNecessary();
	    });
	  },

	  componentDidMount: function componentDidMount() {
	    this.prevTime = _performanceNow2.default();
	    this.startAnimationIfNecessary();
	  },

	  componentWillReceiveProps: function componentWillReceiveProps(props) {
	    if (this.unreadPropStyles != null) {
	      // previous props haven't had the chance to be set yet; set them here
	      this.clearUnreadPropStyle(this.unreadPropStyles);
	    }

	    this.unreadPropStyles = props.styles(this.state.lastIdealStyles);
	    if (this.animationID == null) {
	      this.prevTime = _performanceNow2.default();
	      this.startAnimationIfNecessary();
	    }
	  },

	  componentWillUnmount: function componentWillUnmount() {
	    if (this.animationID != null) {
	      _raf2.default.cancel(this.animationID);
	      this.animationID = null;
	    }
	  },

	  render: function render() {
	    const renderedChildren = this.props.children(this.state.currentStyles);
	    return renderedChildren && _react2.default.Children.only(renderedChildren);
	  },
      });

      exports.default = StaggeredMotion;
      module.exports = exports.default;
      /***/ },
    /* 11 */
    /***/ function (module, exports, __webpack_require__) {
      exports.__esModule = true;

      const _extends = Object.assign || function (target) { for (let i = 1; i < arguments.length; i++) { const source = arguments[i]; for (const key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

      function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

      const _mapToZero = __webpack_require__(2);

      const _mapToZero2 = _interopRequireDefault(_mapToZero);

      const _stripStyle = __webpack_require__(3);

      const _stripStyle2 = _interopRequireDefault(_stripStyle);

      const _stepper3 = __webpack_require__(4);

      const _stepper4 = _interopRequireDefault(_stepper3);

      const _mergeDiff = __webpack_require__(12);

      const _mergeDiff2 = _interopRequireDefault(_mergeDiff);

      const _performanceNow = __webpack_require__(5);

      const _performanceNow2 = _interopRequireDefault(_performanceNow);

      const _raf = __webpack_require__(7);

      const _raf2 = _interopRequireDefault(_raf);

      const _shouldStopAnimation = __webpack_require__(8);

      const _shouldStopAnimation2 = _interopRequireDefault(_shouldStopAnimation);

      const _react = __webpack_require__(9);

      const _react2 = _interopRequireDefault(_react);

      const msPerFrame = 1000 / 60;

      // the children function & (potential) styles function asks as param an
      // Array<TransitionPlainStyle>, where each TransitionPlainStyle is of the format
      // {key: string, data?: any, style: PlainStyle}. However, the way we keep
      // internal states doesn't contain such a data structure (check the state and
      // TransitionMotionState). So when children function and others ask for such
      // data we need to generate them on the fly by combining mergedPropsStyles and
      // currentStyles/lastIdealStyles
      function rehydrateStyles(mergedPropsStyles, unreadPropStyles, plainStyles) {
	  if (unreadPropStyles == null) {
	    // $FlowFixMe
	    return mergedPropsStyles.map((mergedPropsStyle, i) => ({
	        key: mergedPropsStyle.key,
	        data: mergedPropsStyle.data,
	        style: plainStyles[i],
	      }));
	  }
	  return mergedPropsStyles.map((mergedPropsStyle, i) => {
	    // $FlowFixMe
	    for (let j = 0; j < unreadPropStyles.length; j++) {
	      // $FlowFixMe
	      if (unreadPropStyles[j].key === mergedPropsStyle.key) {
	        return {
	          // $FlowFixMe
	          key: unreadPropStyles[j].key,
	          data: unreadPropStyles[j].data,
	          style: plainStyles[i],
	        };
	      }
	    }
	    // $FlowFixMe
	    return { key: mergedPropsStyle.key, data: mergedPropsStyle.data, style: plainStyles[i] };
	  });
      }

      function shouldStopAnimationAll(currentStyles, destStyles, currentVelocities, mergedPropsStyles) {
	  if (mergedPropsStyles.length !== destStyles.length) {
	    return false;
	  }

	  for (var i = 0; i < mergedPropsStyles.length; i++) {
	    if (mergedPropsStyles[i].key !== destStyles[i].key) {
	      return false;
	    }
	  }

	  // we have the invariant that mergedPropsStyles and
	  // currentStyles/currentVelocities/last* are synced in terms of cells, see
	  // mergeAndSync comment for more info
	  for (var i = 0; i < mergedPropsStyles.length; i++) {
	    if (!_shouldStopAnimation2.default(currentStyles[i], destStyles[i].style, currentVelocities[i])) {
	      return false;
	    }
	  }

	  return true;
      }

      // core key merging logic

      // things to do: say previously merged style is {a, b}, dest style (prop) is {b,
      // c}, previous current (interpolating) style is {a, b}
      // **invariant**: current[i] corresponds to merged[i] in terms of key

      // steps:
      // turn merged style into {a?, b, c}
      //    add c, value of c is destStyles.c
      //    maybe remove a, aka call willLeave(a), then merged is either {b, c} or {a, b, c}
      // turn current (interpolating) style from {a, b} into {a?, b, c}
      //    maybe remove a
      //    certainly add c, value of c is willEnter(c)
      // loop over merged and construct new current
      // dest doesn't change, that's owner's
      function mergeAndSync(willEnter, willLeave, oldMergedPropsStyles, destStyles, oldCurrentStyles, oldCurrentVelocities, oldLastIdealStyles, oldLastIdealVelocities) {
	  const newMergedPropsStyles = _mergeDiff2.default(oldMergedPropsStyles, destStyles, (oldIndex, oldMergedPropsStyle) => {
	    const leavingStyle = willLeave(oldMergedPropsStyle);
	    if (leavingStyle == null) {
	      return null;
	    }
	    if (_shouldStopAnimation2.default(oldCurrentStyles[oldIndex], leavingStyle, oldCurrentVelocities[oldIndex])) {
	      return null;
	    }
	    return { key: oldMergedPropsStyle.key, data: oldMergedPropsStyle.data, style: leavingStyle };
	  });

	  const newCurrentStyles = [];
	  const newCurrentVelocities = [];
	  const newLastIdealStyles = [];
	  const newLastIdealVelocities = [];
	  for (let i = 0; i < newMergedPropsStyles.length; i++) {
	    const newMergedPropsStyleCell = newMergedPropsStyles[i];
	    let foundOldIndex = null;
	    for (let j = 0; j < oldMergedPropsStyles.length; j++) {
	      if (oldMergedPropsStyles[j].key === newMergedPropsStyleCell.key) {
	        foundOldIndex = j;
	        break;
	      }
	    }
	    // TODO: key search code
	    if (foundOldIndex == null) {
	      const plainStyle = willEnter(newMergedPropsStyleCell);
	      newCurrentStyles[i] = plainStyle;
	      newLastIdealStyles[i] = plainStyle;

	      // $FlowFixMe
	      const velocity = _mapToZero2.default(newMergedPropsStyleCell.style);
	      newCurrentVelocities[i] = velocity;
	      newLastIdealVelocities[i] = velocity;
	    } else {
	      newCurrentStyles[i] = oldCurrentStyles[foundOldIndex];
	      newLastIdealStyles[i] = oldLastIdealStyles[foundOldIndex];
	      newCurrentVelocities[i] = oldCurrentVelocities[foundOldIndex];
	      newLastIdealVelocities[i] = oldLastIdealVelocities[foundOldIndex];
	    }
	  }

	  return [newMergedPropsStyles, newCurrentStyles, newCurrentVelocities, newLastIdealStyles, newLastIdealVelocities];
      }

      const TransitionMotion = _react2.default.createClass({
	  displayName: 'TransitionMotion',

	  propTypes: {
	    defaultStyles: _react.PropTypes.arrayOf(_react.PropTypes.shape({
	      key: _react.PropTypes.string.isRequired,
	      data: _react.PropTypes.any,
	      style: _react.PropTypes.objectOf(_react.PropTypes.number).isRequired,
	    })),
	    styles: _react.PropTypes.oneOfType([_react.PropTypes.func, _react.PropTypes.arrayOf(_react.PropTypes.shape({
	      key: _react.PropTypes.string.isRequired,
	      data: _react.PropTypes.any,
	      style: _react.PropTypes.objectOf(_react.PropTypes.oneOfType([_react.PropTypes.number, _react.PropTypes.object])).isRequired,
	    }))]).isRequired,
	    children: _react.PropTypes.func.isRequired,
	    willLeave: _react.PropTypes.func,
	    willEnter: _react.PropTypes.func,
	  },

	  getDefaultProps: function getDefaultProps() {
	    return {
	      willEnter: function willEnter(styleThatEntered) {
	        return _stripStyle2.default(styleThatEntered.style);
	      },
	      // recall: returning null makes the current unmounting TransitionStyle
	      // disappear immediately
	      willLeave: function willLeave() {
	        return null;
	      },
	    };
	  },

	  getInitialState: function getInitialState() {
	    const _props = this.props;
	    const { defaultStyles } = _props;
	    const { styles } = _props;
	    const { willEnter } = _props;
	    const { willLeave } = _props;

	    const destStyles = typeof styles === 'function' ? styles(defaultStyles) : styles;

	    // this is special. for the first time around, we don't have a comparison
	    // between last (no last) and current merged props. we'll compute last so:
	    // say default is {a, b} and styles (dest style) is {b, c}, we'll
	    // fabricate last as {a, b}
	    let oldMergedPropsStyles;
	    if (defaultStyles == null) {
	      oldMergedPropsStyles = destStyles;
	    } else {
	      // $FlowFixMe
	      oldMergedPropsStyles = defaultStyles.map((defaultStyleCell) => {
	        // TODO: key search code
	        for (let i = 0; i < destStyles.length; i++) {
	          if (destStyles[i].key === defaultStyleCell.key) {
	            return destStyles[i];
	          }
	        }
	        return defaultStyleCell;
	      });
	    }
	    const oldCurrentStyles = defaultStyles == null ? destStyles.map((s) => _stripStyle2.default(s.style)) : defaultStyles.map((s) => _stripStyle2.default(s.style));
	    const oldCurrentVelocities = defaultStyles == null ? destStyles.map((s) => _mapToZero2.default(s.style)) : defaultStyles.map((s) => _mapToZero2.default(s.style));

	    const _mergeAndSync = mergeAndSync(
	    // $FlowFixMe
	    willEnter,
	    // $FlowFixMe
	    willLeave, oldMergedPropsStyles, destStyles, oldCurrentStyles, oldCurrentVelocities, oldCurrentStyles, // oldLastIdealStyles really
	    oldCurrentVelocities,
          );

	    const mergedPropsStyles = _mergeAndSync[0];
	    const currentStyles = _mergeAndSync[1];
	    const currentVelocities = _mergeAndSync[2];
	    const lastIdealStyles = _mergeAndSync[3];
	    const lastIdealVelocities = _mergeAndSync[4];
	    // oldLastIdealVelocities really

	    return {
	      currentStyles,
	      currentVelocities,
	      lastIdealStyles,
	      lastIdealVelocities,
	      mergedPropsStyles,
	    };
	  },

	  animationID: null,
	  prevTime: 0,
	  accumulatedTime: 0,
	  // it's possible that currentStyle's value is stale: if props is immediately
	  // changed from 0 to 400 to spring(0) again, the async currentStyle is still
	  // at 0 (didn't have time to tick and interpolate even once). If we naively
	  // compare currentStyle with destVal it'll be 0 === 0 (no animation, stop).
	  // In reality currentStyle should be 400
	  unreadPropStyles: null,
	  // after checking for unreadPropStyles != null, we manually go set the
	  // non-interpolating values (those that are a number, without a spring
	  // config)
	  clearUnreadPropStyle: function clearUnreadPropStyle(unreadPropStyles) {
	    const _mergeAndSync2 = mergeAndSync(
	    // $FlowFixMe
	    this.props.willEnter,
	    // $FlowFixMe
	    this.props.willLeave, this.state.mergedPropsStyles, unreadPropStyles, this.state.currentStyles, this.state.currentVelocities, this.state.lastIdealStyles, this.state.lastIdealVelocities,
          );

	    const mergedPropsStyles = _mergeAndSync2[0];
	    const currentStyles = _mergeAndSync2[1];
	    const currentVelocities = _mergeAndSync2[2];
	    const lastIdealStyles = _mergeAndSync2[3];
	    const lastIdealVelocities = _mergeAndSync2[4];

	    for (let i = 0; i < unreadPropStyles.length; i++) {
	      const unreadPropStyle = unreadPropStyles[i].style;
	      let dirty = false;

	      for (const key in unreadPropStyle) {
	        if (!unreadPropStyle.hasOwnProperty(key)) {
	          continue;
	        }

	        const styleValue = unreadPropStyle[key];
	        if (typeof styleValue === 'number') {
	          if (!dirty) {
	            dirty = true;
	            currentStyles[i] = { ...currentStyles[i] };
	            currentVelocities[i] = { ...currentVelocities[i] };
	            lastIdealStyles[i] = { ...lastIdealStyles[i] };
	            lastIdealVelocities[i] = { ...lastIdealVelocities[i] };
	            mergedPropsStyles[i] = {
	              key: mergedPropsStyles[i].key,
	              data: mergedPropsStyles[i].data,
	              style: { ...mergedPropsStyles[i].style },
	            };
	          }
	          currentStyles[i][key] = styleValue;
	          currentVelocities[i][key] = 0;
	          lastIdealStyles[i][key] = styleValue;
	          lastIdealVelocities[i][key] = 0;
	          mergedPropsStyles[i].style[key] = styleValue;
	        }
	      }
	    }

	    // unlike the other 2 components, we can't detect staleness and optionally
	    // opt out of setState here. each style object's data might contain new
	    // stuff we're not/cannot compare
	    this.setState({
	      currentStyles,
	      currentVelocities,
	      mergedPropsStyles,
	      lastIdealStyles,
	      lastIdealVelocities,
	    });
	  },

	  startAnimationIfNecessary: function startAnimationIfNecessary() {
	    const _this = this;

	    // TODO: when config is {a: 10} and dest is {a: 10} do we raf once and
	    // call cb? No, otherwise accidental parent rerender causes cb trigger
	    this.animationID = _raf2.default(() => {
	      const propStyles = _this.props.styles;
	      const destStyles = typeof propStyles === 'function' ? propStyles(rehydrateStyles(_this.state.mergedPropsStyles, _this.unreadPropStyles, _this.state.lastIdealStyles)) : propStyles;

	      // check if we need to animate in the first place
	      if (shouldStopAnimationAll(_this.state.currentStyles, destStyles, _this.state.currentVelocities, _this.state.mergedPropsStyles)) {
	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.accumulatedTime = 0;
	        return;
	      }

	      const currentTime = _performanceNow2.default();
	      const timeDelta = currentTime - _this.prevTime;
	      _this.prevTime = currentTime;
	      _this.accumulatedTime += timeDelta;
	      // more than 10 frames? prolly switched browser tab. Restart
	      if (_this.accumulatedTime > msPerFrame * 10) {
	        _this.accumulatedTime = 0;
	      }

	      if (_this.accumulatedTime === 0) {
	        // no need to cancel animationID here; shouldn't have any in flight
	        _this.animationID = null;
	        _this.startAnimationIfNecessary();
	        return;
	      }

	      const currentFrameCompletion = (_this.accumulatedTime - Math.floor(_this.accumulatedTime / msPerFrame) * msPerFrame) / msPerFrame;
	      const framesToCatchUp = Math.floor(_this.accumulatedTime / msPerFrame);

	      const _mergeAndSync3 = mergeAndSync(
	      // $FlowFixMe
	      _this.props.willEnter,
	      // $FlowFixMe
	      _this.props.willLeave, _this.state.mergedPropsStyles, destStyles, _this.state.currentStyles, _this.state.currentVelocities, _this.state.lastIdealStyles, _this.state.lastIdealVelocities,
            );

	      const newMergedPropsStyles = _mergeAndSync3[0];
	      const newCurrentStyles = _mergeAndSync3[1];
	      const newCurrentVelocities = _mergeAndSync3[2];
	      const newLastIdealStyles = _mergeAndSync3[3];
	      const newLastIdealVelocities = _mergeAndSync3[4];

	      for (let i = 0; i < newMergedPropsStyles.length; i++) {
	        const newMergedPropsStyle = newMergedPropsStyles[i].style;
	        const newCurrentStyle = {};
	        const newCurrentVelocity = {};
	        const newLastIdealStyle = {};
	        const newLastIdealVelocity = {};

	        for (const key in newMergedPropsStyle) {
	          if (!newMergedPropsStyle.hasOwnProperty(key)) {
	            continue;
	          }

	          const styleValue = newMergedPropsStyle[key];
	          if (typeof styleValue === 'number') {
	            newCurrentStyle[key] = styleValue;
	            newCurrentVelocity[key] = 0;
	            newLastIdealStyle[key] = styleValue;
	            newLastIdealVelocity[key] = 0;
	          } else {
	            let newLastIdealStyleValue = newLastIdealStyles[i][key];
	            let newLastIdealVelocityValue = newLastIdealVelocities[i][key];
	            for (let j = 0; j < framesToCatchUp; j++) {
	              const _stepper = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	              newLastIdealStyleValue = _stepper[0];
	              newLastIdealVelocityValue = _stepper[1];
	            }

	            const _stepper2 = _stepper4.default(msPerFrame / 1000, newLastIdealStyleValue, newLastIdealVelocityValue, styleValue.val, styleValue.stiffness, styleValue.damping, styleValue.precision);

	            const nextIdealX = _stepper2[0];
	            const nextIdealV = _stepper2[1];

	            newCurrentStyle[key] = newLastIdealStyleValue + (nextIdealX - newLastIdealStyleValue) * currentFrameCompletion;
	            newCurrentVelocity[key] = newLastIdealVelocityValue + (nextIdealV - newLastIdealVelocityValue) * currentFrameCompletion;
	            newLastIdealStyle[key] = newLastIdealStyleValue;
	            newLastIdealVelocity[key] = newLastIdealVelocityValue;
	          }
	        }

	        newLastIdealStyles[i] = newLastIdealStyle;
	        newLastIdealVelocities[i] = newLastIdealVelocity;
	        newCurrentStyles[i] = newCurrentStyle;
	        newCurrentVelocities[i] = newCurrentVelocity;
	      }

	      _this.animationID = null;
	      // the amount we're looped over above
	      _this.accumulatedTime -= framesToCatchUp * msPerFrame;

	      _this.setState({
	        currentStyles: newCurrentStyles,
	        currentVelocities: newCurrentVelocities,
	        lastIdealStyles: newLastIdealStyles,
	        lastIdealVelocities: newLastIdealVelocities,
	        mergedPropsStyles: newMergedPropsStyles,
	      });

	      _this.unreadPropStyles = null;

	      _this.startAnimationIfNecessary();
	    });
	  },

	  componentDidMount: function componentDidMount() {
	    this.prevTime = _performanceNow2.default();
	    this.startAnimationIfNecessary();
	  },

	  componentWillReceiveProps: function componentWillReceiveProps(props) {
	    if (this.unreadPropStyles) {
	      // previous props haven't had the chance to be set yet; set them here
	      this.clearUnreadPropStyle(this.unreadPropStyles);
	    }

	    if (typeof props.styles === 'function') {
	      // $FlowFixMe
	      this.unreadPropStyles = props.styles(rehydrateStyles(this.state.mergedPropsStyles, this.unreadPropStyles, this.state.lastIdealStyles));
	    } else {
	      this.unreadPropStyles = props.styles;
	    }

	    if (this.animationID == null) {
	      this.prevTime = _performanceNow2.default();
	      this.startAnimationIfNecessary();
	    }
	  },

	  componentWillUnmount: function componentWillUnmount() {
	    if (this.animationID != null) {
	      _raf2.default.cancel(this.animationID);
	      this.animationID = null;
	    }
	  },

	  render: function render() {
	    const hydratedStyles = rehydrateStyles(this.state.mergedPropsStyles, this.unreadPropStyles, this.state.currentStyles);
	    const renderedChildren = this.props.children(hydratedStyles);
	    return renderedChildren && _react2.default.Children.only(renderedChildren);
	  },
      });

      exports.default = TransitionMotion;
      module.exports = exports.default;

      // list of styles, each containing interpolating values. Part of what's passed
      // to children function. Notice that this is
      // Array<ActualInterpolatingStyleObject>, without the wrapper that is {key: ...,
      // data: ... style: ActualInterpolatingStyleObject}. Only mergedPropsStyles
      // contains the key & data info (so that we only have a single source of truth
      // for these, and to save space). Check the comment for `rehydrateStyles` to
      // see how we regenerate the entirety of what's passed to children function

      // the array that keeps track of currently rendered stuff! Including stuff
      // that you've unmounted but that's still animating. This is where it lives
      /***/ },
    /* 12 */
    /***/ function (module, exports) {
      // core keys merging algorithm. If previous render's keys are [a, b], and the
      // next render's [c, b, d], what's the final merged keys and ordering?

      // - c and a must both be before b
      // - b before d
      // - ordering between a and c ambiguous

      // this reduces to merging two partially ordered lists (e.g. lists where not
      // every item has a definite ordering, like comparing a and c above). For the
      // ambiguous ordering we deterministically choose to place the next render's
      // item after the previous'; so c after a

      // this is called a topological sorting. Except the existing algorithms don't
      // work well with js bc of the amount of allocation, and isn't optimized for our
      // current use-case bc the runtime is linear in terms of edges (see wiki for
      // meaning), which is huge when two lists have many common elements

      exports.__esModule = true;
      exports.default = mergeDiff;

      function mergeDiff(prev, next, onRemove) {
	  // bookkeeping for easier access of a key's index below. This is 2 allocations +
	  // potentially triggering chrome hash map mode for objs (so it might be faster

	  const prevKeyIndex = {};
	  for (var i = 0; i < prev.length; i++) {
	    prevKeyIndex[prev[i].key] = i;
	  }
	  const nextKeyIndex = {};
	  for (var i = 0; i < next.length; i++) {
	    nextKeyIndex[next[i].key] = i;
	  }

	  // first, an overly elaborate way of merging prev and next, eliminating
	  // duplicates (in terms of keys). If there's dupe, keep the item in next).
	  // This way of writing it saves allocations
	  const ret = [];
	  for (var i = 0; i < next.length; i++) {
	    ret[i] = next[i];
	  }
	  for (var i = 0; i < prev.length; i++) {
	    if (!nextKeyIndex.hasOwnProperty(prev[i].key)) {
	      // this is called my TM's `mergeAndSync`, which calls willLeave. We don't
	      // merge in keys that the user desires to kill
	      const fill = onRemove(i, prev[i]);
	      if (fill != null) {
	        ret.push(fill);
	      }
	    }
	  }

	  // now all the items all present. Core sorting logic to have the right order
	  return ret.sort((a, b) => {
	    const nextOrderA = nextKeyIndex[a.key];
	    const nextOrderB = nextKeyIndex[b.key];
	    const prevOrderA = prevKeyIndex[a.key];
	    const prevOrderB = prevKeyIndex[b.key];

	    if (nextOrderA != null && nextOrderB != null) {
	      // both keys in next
	      return nextKeyIndex[a.key] - nextKeyIndex[b.key];
	    } if (prevOrderA != null && prevOrderB != null) {
	      // both keys in prev
	      return prevKeyIndex[a.key] - prevKeyIndex[b.key];
	    } if (nextOrderA != null) {
	      // key a in next, key b in prev

	      // how to determine the order between a and b? We find a "pivot" (term
	      // abuse), a key present in both prev and next, that is sandwiched between
	      // a and b. In the context of our above example, if we're comparing a and
	      // d, b's (the only) pivot
	      for (var i = 0; i < next.length; i++) {
	        var pivot = next[i].key;
	        if (!prevKeyIndex.hasOwnProperty(pivot)) {
	          continue;
	        }

	        if (nextOrderA < nextKeyIndex[pivot] && prevOrderB > prevKeyIndex[pivot]) {
	          return -1;
	        } if (nextOrderA > nextKeyIndex[pivot] && prevOrderB < prevKeyIndex[pivot]) {
	          return 1;
	        }
	      }
	      // pluggable. default to: next bigger than prev
	      return 1;
	    }
	    // prevOrderA, nextOrderB
	    for (var i = 0; i < next.length; i++) {
	      var pivot = next[i].key;
	      if (!prevKeyIndex.hasOwnProperty(pivot)) {
	        continue;
	      }
	      if (nextOrderB < nextKeyIndex[pivot] && prevOrderA > prevKeyIndex[pivot]) {
	        return 1;
	      } if (nextOrderB > nextKeyIndex[pivot] && prevOrderA < prevKeyIndex[pivot]) {
	        return -1;
	      }
	    }
	    // pluggable. default to: next bigger than prev
	    return -1;
	  });
      }

      module.exports = exports.default;
      // to loop through and find a key's index each time), but I no longer care
      /***/ },
    /* 13 */
    /***/ function (module, exports, __webpack_require__) {
      exports.__esModule = true;

      const _extends = Object.assign || function (target) { for (let i = 1; i < arguments.length; i++) { const source = arguments[i]; for (const key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

      exports.default = spring;

      function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

      const _presets = __webpack_require__(14);

      const _presets2 = _interopRequireDefault(_presets);

      const defaultConfig = { ..._presets2.default.noWobble, precision: 0.01 };

      function spring(val, config) {
	  return { ...defaultConfig, ...config, val };
      }

      module.exports = exports.default;
      /***/ },
    /* 14 */
    /***/ function (module, exports) {
      exports.__esModule = true;
      exports.default = {
	  noWobble: { stiffness: 170, damping: 26 }, // the default, if nothing provided
	  gentle: { stiffness: 120, damping: 14 },
	  wobbly: { stiffness: 180, damping: 12 },
	  stiff: { stiffness: 210, damping: 20 },
      };
      module.exports = exports.default;
      /***/ },
    /* 15 */
    /***/ function (module, exports, __webpack_require__) {
      /* WEBPACK VAR INJECTION */(function (process) {
        exports.__esModule = true;
        exports.default = reorderKeys;

        let hasWarned = false;

        function reorderKeys() {
	  if (process.env.NODE_ENV === 'development') {
	    if (!hasWarned) {
	      hasWarned = true;
	      console.error('`reorderKeys` has been removed, since it is no longer needed for TransitionMotion\'s new styles array API.');
	    }
	  }
        }

        module.exports = exports.default;
        /* WEBPACK VAR INJECTION */ }.call(exports, __webpack_require__(6)));
      /***/ },
    /** *** */ ]))));

// # sourceMappingURL=react-motion.map
