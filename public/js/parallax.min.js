!(function (t, i, e) {
  function s(t, i) {
    this.element = t, this.layers = t.getElementsByClassName('layer'); const e = {
      calibrateX: this.data(this.element, 'calibrate-x'), calibrateY: this.data(this.element, 'calibrate-y'), invertX: this.data(this.element, 'invert-x'), invertY: this.data(this.element, 'invert-y'), limitX: this.data(this.element, 'limit-x'), limitY: this.data(this.element, 'limit-y'), scalarX: this.data(this.element, 'scalar-x'), scalarY: this.data(this.element, 'scalar-y'), frictionX: this.data(this.element, 'friction-x'), frictionY: this.data(this.element, 'friction-y'), originX: this.data(this.element, 'origin-x'), originY: this.data(this.element, 'origin-y'),
    }; for (const s in e)e[s] === null && delete e[s]; this.extend(this, r, i, e), this.calibrationTimer = null, this.calibrationFlag = !0, this.enabled = !1, this.depths = [], this.raf = null, this.bounds = null, this.ex = 0, this.ey = 0, this.ew = 0, this.eh = 0, this.ecx = 0, this.ecy = 0, this.erx = 0, this.ery = 0, this.cx = 0, this.cy = 0, this.ix = 0, this.iy = 0, this.mx = 0, this.my = 0, this.vx = 0, this.vy = 0, this.onMouseMove = this.onMouseMove.bind(this), this.onDeviceOrientation = this.onDeviceOrientation.bind(this), this.onOrientationTimer = this.onOrientationTimer.bind(this), this.onCalibrationTimer = this.onCalibrationTimer.bind(this), this.onAnimationFrame = this.onAnimationFrame.bind(this), this.onWindowResize = this.onWindowResize.bind(this), this.initialise();
  } const n = 'Parallax'; const o = 30; var r = {
    relativeInput: !1, clipRelativeInput: !1, calibrationThreshold: 100, calibrationDelay: 500, supportDelay: 500, calibrateX: !1, calibrateY: !0, invertX: !0, invertY: !0, limitX: !1, limitY: !1, scalarX: 10, scalarY: 10, frictionX: 0.1, frictionY: 0.1, originX: 0.5, originY: 0.5,
  }; s.prototype.extend = function () { if (arguments.length > 1) for (let t = arguments[0], i = 1, e = arguments.length; e > i; i++) { const s = arguments[i]; for (const n in s)t[n] = s[n]; } }, s.prototype.data = function (t, i) { return this.deserialize(t.getAttribute(`data-${i}`)); }, s.prototype.deserialize = function (t) { return t === 'true' ? !0 : t === 'false' ? !1 : t === 'null' ? null : !isNaN(parseFloat(t)) && isFinite(t) ? parseFloat(t) : t; }, s.prototype.camelCase = function (t) { return t.replace(/-+(.)?/g, (t, i) => (i ? i.toUpperCase() : '')); }, s.prototype.transformSupport = function (s) { for (var n = i.createElement('div'), o = !1, r = null, a = !1, h = null, l = null, p = 0, c = this.vendors.length; c > p; p++) if (this.vendors[p] !== null ? (h = `${this.vendors[p][0]}transform`, l = `${this.vendors[p][1]}Transform`) : (h = 'transform', l = 'transform'), n.style[l] !== e) { o = !0; break; } switch (s) { case '2D': a = o; break; case '3D': if (o) { const m = i.body || i.createElement('body'); const u = i.documentElement; const y = u.style.overflow; i.body || (u.style.overflow = 'hidden', u.appendChild(m), m.style.overflow = 'hidden', m.style.background = ''), m.appendChild(n), n.style[l] = 'translate3d(1px,1px,1px)', r = t.getComputedStyle(n).getPropertyValue(h), a = r !== e && r.length > 0 && r !== 'none', u.style.overflow = y, m.removeChild(n); } } return a; }, s.prototype.ww = null, s.prototype.wh = null, s.prototype.wcx = null, s.prototype.wcy = null, s.prototype.wrx = null, s.prototype.wry = null, s.prototype.portrait = null, s.prototype.desktop = !navigator.userAgent.match(/(iPhone|iPod|iPad|Android|BlackBerry|BB10|mobi|tablet|opera mini|nexus 7)/i), s.prototype.vendors = [null, ['-webkit-', 'webkit'], ['-moz-', 'Moz'], ['-o-', 'O'], ['-ms-', 'ms']], s.prototype.motionSupport = !!t.DeviceMotionEvent, s.prototype.orientationSupport = !!t.DeviceOrientationEvent, s.prototype.orientationStatus = 0, s.prototype.transform2DSupport = s.prototype.transformSupport('2D'), s.prototype.transform3DSupport = s.prototype.transformSupport('3D'), s.prototype.propertyCache = {}, s.prototype.initialise = function () { this.transform3DSupport && this.accelerate(this.element); const i = t.getComputedStyle(this.element); i.getPropertyValue('position') === 'static' && (this.element.style.position = 'relative'), this.updateLayers(), this.updateDimensions(), this.enable(), this.queueCalibration(this.calibrationDelay); }, s.prototype.updateLayers = function () { this.layers = this.element.getElementsByClassName('layer'), this.depths = []; for (let t = 0, i = this.layers.length; i > t; t++) { const e = this.layers[t]; this.transform3DSupport && this.accelerate(e), e.style.position = t ? 'absolute' : 'relative', e.style.display = 'block', e.style.left = 0, e.style.top = 0, this.depths.push(this.data(e, 'depth') || 0); } }, s.prototype.updateDimensions = function () { this.ww = t.innerWidth, this.wh = t.innerHeight, this.wcx = this.ww * this.originX, this.wcy = this.wh * this.originY, this.wrx = Math.max(this.wcx, this.ww - this.wcx), this.wry = Math.max(this.wcy, this.wh - this.wcy); }, s.prototype.updateBounds = function () { this.bounds = this.element.getBoundingClientRect(), this.ex = this.bounds.left, this.ey = this.bounds.top, this.ew = this.bounds.width, this.eh = this.bounds.height, this.ecx = this.ew * this.originX, this.ecy = this.eh * this.originY, this.erx = Math.max(this.ecx, this.ew - this.ecx), this.ery = Math.max(this.ecy, this.eh - this.ecy); }, s.prototype.queueCalibration = function (t) { clearTimeout(this.calibrationTimer), this.calibrationTimer = setTimeout(this.onCalibrationTimer, t); }, s.prototype.enable = function () { this.enabled || (this.enabled = !0, this.orientationSupport ? (this.portrait = null, t.addEventListener('deviceorientation', this.onDeviceOrientation), setTimeout(this.onOrientationTimer, this.supportDelay)) : (this.cx = 0, this.cy = 0, this.portrait = !1, t.addEventListener('mousemove', this.onMouseMove)), t.addEventListener('resize', this.onWindowResize), this.raf = requestAnimationFrame(this.onAnimationFrame)); }, s.prototype.disable = function () { this.enabled && (this.enabled = !1, this.orientationSupport ? t.removeEventListener('deviceorientation', this.onDeviceOrientation) : t.removeEventListener('mousemove', this.onMouseMove), t.removeEventListener('resize', this.onWindowResize), cancelAnimationFrame(this.raf)); }, s.prototype.calibrate = function (t, i) { this.calibrateX = t === e ? this.calibrateX : t, this.calibrateY = i === e ? this.calibrateY : i; }, s.prototype.invert = function (t, i) { this.invertX = t === e ? this.invertX : t, this.invertY = i === e ? this.invertY : i; }, s.prototype.friction = function (t, i) { this.frictionX = t === e ? this.frictionX : t, this.frictionY = i === e ? this.frictionY : i; }, s.prototype.scalar = function (t, i) { this.scalarX = t === e ? this.scalarX : t, this.scalarY = i === e ? this.scalarY : i; }, s.prototype.limit = function (t, i) { this.limitX = t === e ? this.limitX : t, this.limitY = i === e ? this.limitY : i; }, s.prototype.origin = function (t, i) { this.originX = t === e ? this.originX : t, this.originY = i === e ? this.originY : i; }, s.prototype.clamp = function (t, i, e) { return t = Math.max(t, i), t = Math.min(t, e); }, s.prototype.css = function (t, i, s) { let n = this.propertyCache[i]; if (!n) for (let o = 0, r = this.vendors.length; r > o; o++) if (n = this.vendors[o] !== null ? this.camelCase(`${this.vendors[o][1]}-${i}`) : i, t.style[n] !== e) { this.propertyCache[i] = n; break; }t.style[n] = s; }, s.prototype.accelerate = function (t) { this.css(t, 'transform', 'translate3d(0,0,0)'), this.css(t, 'transform-style', 'preserve-3d'), this.css(t, 'backface-visibility', 'hidden'); }, s.prototype.setPosition = function (t, i, e) { i += 'px', e += 'px', this.transform3DSupport ? this.css(t, 'transform', `translate3d(${i},${e},0)`) : this.transform2DSupport ? this.css(t, 'transform', `translate(${i},${e})`) : (t.style.left = i, t.style.top = e); }, s.prototype.onOrientationTimer = function () { this.orientationSupport && this.orientationStatus === 0 && (this.disable(), this.orientationSupport = !1, this.enable()); }, s.prototype.onCalibrationTimer = function () { this.calibrationFlag = !0; }, s.prototype.onWindowResize = function () { this.updateDimensions(); }, s.prototype.onAnimationFrame = function () { this.updateBounds(); const t = this.ix - this.cx; const i = this.iy - this.cy; (Math.abs(t) > this.calibrationThreshold || Math.abs(i) > this.calibrationThreshold) && this.queueCalibration(0), this.portrait ? (this.mx = this.calibrateX ? i : this.iy, this.my = this.calibrateY ? t : this.ix) : (this.mx = this.calibrateX ? t : this.ix, this.my = this.calibrateY ? i : this.iy), this.mx *= this.ew * (this.scalarX / 100), this.my *= this.eh * (this.scalarY / 100), isNaN(parseFloat(this.limitX)) || (this.mx = this.clamp(this.mx, -this.limitX, this.limitX)), isNaN(parseFloat(this.limitY)) || (this.my = this.clamp(this.my, -this.limitY, this.limitY)), this.vx += (this.mx - this.vx) * this.frictionX, this.vy += (this.my - this.vy) * this.frictionY; for (let e = 0, s = this.layers.length; s > e; e++) { const n = this.layers[e]; const o = this.depths[e]; const r = this.vx * o * (this.invertX ? -1 : 1); const a = this.vy * o * (this.invertY ? -1 : 1); this.setPosition(n, r, a); } this.raf = requestAnimationFrame(this.onAnimationFrame); }, s.prototype.onDeviceOrientation = function (t) { if (!this.desktop && t.beta !== null && t.gamma !== null) { this.orientationStatus = 1; const i = (t.beta || 0) / o; const e = (t.gamma || 0) / o; const s = this.wh > this.ww; this.portrait !== s && (this.portrait = s, this.calibrationFlag = !0), this.calibrationFlag && (this.calibrationFlag = !1, this.cx = i, this.cy = e), this.ix = i, this.iy = e; } }, s.prototype.onMouseMove = function (t) { let i = t.clientX; let e = t.clientY; !this.orientationSupport && this.relativeInput ? (this.clipRelativeInput && (i = Math.max(i, this.ex), i = Math.min(i, this.ex + this.ew), e = Math.max(e, this.ey), e = Math.min(e, this.ey + this.eh)), this.ix = (i - this.ex - this.ecx) / this.erx, this.iy = (e - this.ey - this.ecy) / this.ery) : (this.ix = (i - this.wcx) / this.wrx, this.iy = (e - this.wcy) / this.wry); }, t[n] = s;
}(window, document)), (function () { for (var t = 0, i = ['ms', 'moz', 'webkit', 'o'], e = 0; e < i.length && !window.requestAnimationFrame; ++e)window.requestAnimationFrame = window[`${i[e]}RequestAnimationFrame`], window.cancelAnimationFrame = window[`${i[e]}CancelAnimationFrame`] || window[`${i[e]}CancelRequestAnimationFrame`]; window.requestAnimationFrame || (window.requestAnimationFrame = function (i) { const e = (new Date()).getTime(); const s = Math.max(0, 16 - (e - t)); const n = window.setTimeout(() => { i(e + s); }, s); return t = e + s, n; }), window.cancelAnimationFrame || (window.cancelAnimationFrame = function (t) { clearTimeout(t); }); }());
