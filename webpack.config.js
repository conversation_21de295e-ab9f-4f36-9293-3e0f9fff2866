const path = require('path');
const webpack = require('webpack');
const nodeExternals = require('webpack-node-externals');

module.exports = (env, argv) => {
  const mode = argv.mode || 'development'; // dev mode by default

  return {
    mode,
    target: 'node',
    stats: mode === 'development' ? 'errors-warnings' : 'normal',
    devtool: mode === 'development' ? 'source-map' : false,
    entry: {
      app: ['./src/index.js']
    },
    output: {
      path: path.resolve(__dirname, 'build'),
      filename: 'bundle.js',
      publicPath: 'build/',
    },
    externals: [nodeExternals()],
    module: {
      rules: [
        {
          test: /\.(?:js|mjs|cjs)$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: [
                ['@babel/preset-env', { targets: "defaults" }]
              ]
            }
          }
        }
      ]
    },
    resolve: {
      alias: {
        '@functions': path.resolve(__dirname, './src/database/functions'),
        '@validators': path.resolve(__dirname, './src/validators'),
        '@postgresql': path.resolve(__dirname, './src/database/pgConnection.js'),
        '@sqlserver': path.resolve(__dirname, './src/database/mssqlConnection.js'),
      },
    },
    plugins: [
      new webpack.IgnorePlugin({ resourceRegExp: /^pg-native$/ }),
    ],
    ignoreWarnings: [{ module: /node_modules/ }]
  }
};
