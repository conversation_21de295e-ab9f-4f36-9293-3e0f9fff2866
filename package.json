{"name": "ccorresponsales-back", "version": "1.0.0", "description": "Código para el servicio RESTful API de Corresponsales", "main": "src/index.js", "engines": {"node": ">=22.0.0"}, "scripts": {"test": "npm run lint && npm run unit-test", "unit-test": "mocha", "build": "npm run clean && webpack --mode none --progress", "clean": "rimraf ./build", "dev": "npm-run-all --parallel watch:server watch:build watch:lint", "start": "NODE_ENV=production node ./build/bundle.js", "watch:build": "webpack --watch", "watch:server": "nodemon --inspect=\"9229\" --watch \"./build\" ./build/bundle.js", "watch:lint": "node node_modules/eslint-watch/bin/esw -w --fix", "lint": "eslint --fix --ext .js ."}, "author": "GAMAN SOLUTIONS", "license": "ISC", "dependencies": {"axios": "^1.9.0", "body-parser": "^2.2.0", "chalk": "^5.4.1", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "fs": "0.0.1-security", "helmet": "^8.1.0", "http-errors": "^2.0.0", "joi": "^17.13.3", "js2xmlparser": "^5.0.0", "jsonwebtoken": "^9.0.2", "luxon": "^3.6.1", "mime-types": "^3.0.1", "mkdirp": "^3.0.1", "morgan": "^1.10.0", "mssql": "^11.0.1", "multer": "1.4.5-lts.2", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "pg": "^8.16.0", "regenerator-runtime": "^0.14.1", "rotating-file-stream": "^3.2.6", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "validator": "^13.15.0", "winston": "^3.17.0", "yaml": "^2.8.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/preset-env": "^7.27.2", "babel-loader": "^10.0.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "eslint": "^8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-watch": "^8.0.0", "nodemon": "^3.1.10", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "webpack": "^5.99.8", "webpack-cli": "^6.0.1", "webpack-node-externals": "^3.0.0"}, "overrides": {"cross-spawn": "7.0.6", "debug": "4.4.1", "glob": "11.0.2"}}