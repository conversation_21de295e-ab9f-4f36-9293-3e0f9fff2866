import { v4 } from 'uuid';
import { DateTime } from 'luxon';
import chalk from 'chalk';
import createError from 'http-errors';
import morgan from 'morgan';

import { RATE_LIMIT } from '../constants/mensajes';

const DEV = process.env.NODE_ENV !== 'production';

export const corsOptions = {
  origin: process.env.CORS_ORIGIN,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Idp'],
  exposedHeaders: ['Content-Disposition', 'Content-Length', 'Content-Transfer-Encoding'],
};

export const helmetOptions = {
  strictTransportSecurity: { // HSTS
    maxAge: 31536000, // 1 año
    includeSubDomains: false,
    preload: true,
  },
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'"],
      imgSrc: ["'self'"],
    },
  },
};

export const limiterOptions = {
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: DEV ? 9999 : 250, // Limita cada IP a 100 peticiones por 'ventana' (aquí, por 15 minutos)
  standardHeaders: true, // Retorna los headers estándar de la librería `rate-limit`
  legacyHeaders: false, // Deshabilita los `X-RateLimit-*` headers
  message: RATE_LIMIT, // Mensaje de error,
  handler: (req, res, next) => {
    if (DEV) {
      logger.info(`Cliente con dirección: ${chalk.bold(req.ip)} bloqueado por exceder el límite de peticiones`);
    }
    next(createError(429, RATE_LIMIT));
  },
  keyGenerator: (req) => req.headers['x-forwarded-for'] || req.connection.remoteAddress,
};

export const morganInit = () => {
  const statusColor = (status) => {
    if (status >= 500) {
      return chalk.red(status);
    }
    if (status >= 400) {
      return chalk.yellow(status);
    }
    if (status >= 300) {
      return chalk.cyan(status);
    }
    return chalk.green(status);
  };

  // eslint-disable-next-line max-len
  morgan.token('custom-time', () => `[${chalk.gray(`${DateTime.fromISO(DateTime.now()).toFormat('dd-MM-yyyy HH:mm:ss')}`)}]`);
  morgan.token('custom-method', (req) => `[${chalk.cyanBright(req.method)}]:`);
  morgan.token('custom-status', (req, res) => statusColor(res.statusCode));
  morgan.token('id', () => v4());
};
