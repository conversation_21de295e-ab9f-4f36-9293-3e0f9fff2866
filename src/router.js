import bodyParser from 'body-parser';
import compression from 'compression';
import cors from 'cors';
import createError from 'http-errors';
import helmet from 'helmet';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import swaggerUi from 'swagger-ui-express';

/**
 * Middlewares
 */
import { manejadorErrores, validarSesion } from './middlewares';

/**
 * Controladores
 */
import archivoManual from './controllers/archivo-manual';
import bitacoraAccionCorrecciones from './controllers/bitacora-accion-correcciones';
import bitacoraConciliaciones from './controllers/bitacora-conciliaciones';
import catalogos from './controllers/catalogos';
import conciliacionBloqueos from './controllers/conciliacion-bloqueos';
import estadisticas from './controllers/estadisticas';
import perfiles from './controllers/perfiles';
import procesosExternos from './controllers/procesos-externos';
import sesiones from './controllers/sesiones';
import tiecarmovhistorial from './controllers/tiecarmovhistorial';
import usuarios from './controllers/usuarios';
import correos from './controllers/correos';

/**
 * Utilidades
 */
import {
  corsOptions,
  helmetOptions,
  limiterOptions,
  morganInit,
} from './config/servidor';
import passport from './utilities/azure';
import crearApiDocs from '../docs';
import { NOT_FOUND } from './constants/mensajes';

const rfs = require('rotating-file-stream');

const accessLogStream = rfs.createStream('access.log', {
  interval: '1d',
  path: 'logs/access/',
  compress: true,
});

export default (app) => {
  const isDev = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV.includes('test');

  const versiones = {
    v1: (uri = '') => `/api/v1/${uri}`,
  };

  morganInit();

  /**
   * Middleware para el servicio
   * * Morgan para logs de peticiones
   * * Rate Limit limita las peticiones a 100 por 15 minutos
   * * CORS para permitir peticiones desde otros dominios
   * * Compresión de respuestas
   * * Body parser para peticiones con JSON o URL-encoded
   */
  if (isDev || isTest) {
    app.use('/api-docs', swaggerUi.serve, crearApiDocs());
    // eslint-disable-next-line max-len
    app.use(morgan(':custom-time :custom-method :remote-addr - :url - :custom-status - :response-time ms - :res[content-length]'));
  } else {
    // eslint-disable-next-line max-len
    app.use(morgan(':id - :remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length]', { stream: accessLogStream }));
  }

  app.use(cors(corsOptions));
  app.use(helmet(helmetOptions));
  app.set('trust proxy', true);
  app.use(rateLimit(limiterOptions));
  app.use(compression());
  app.use(bodyParser.urlencoded({ limit: '5mb', extended: true }));
  app.use(bodyParser.json({ limit: '5mb', extended: true }));

  /**
   * Passport para autenticación a través de Azure AD
   */
  app.use(passport.initialize());

  /**
   * Rutas
   * * Ruta de prueba
   * * Rutas de la API (v1)
   */

  app.use(validarSesion);
  app.use(versiones.v1('acciones-correcciones'), bitacoraAccionCorrecciones);
  app.use(versiones.v1('archivo-manual'), archivoManual);
  app.use(versiones.v1('bitacora-conciliacion'), bitacoraConciliaciones);
  app.use(versiones.v1('catalogos'), catalogos);
  app.use(versiones.v1('conciliacion-bloqueos'), conciliacionBloqueos);
  app.use(versiones.v1('estadisticas'), estadisticas);
  app.use(versiones.v1('perfiles'), perfiles);
  app.use(versiones.v1('procesos-externos'), procesosExternos);
  app.use(versiones.v1('sesiones'), sesiones);
  app.use(versiones.v1('tiecarmovhistorial'), tiecarmovhistorial);
  app.use(versiones.v1('usuarios'), usuarios);
  app.use(versiones.v1('correos'), correos);

  /**
   * Middleware para manejo de errores 404
   */
  app.use((req, res, next) => {
    next(createError(404, NOT_FOUND));
  });

  /**
   * Middleware para manejo de errores 500
   */
  app.use(manejadorErrores);
};
