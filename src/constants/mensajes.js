/* eslint-disable max-len */
import chalk from 'chalk';

import { CONCILIACIONES, TipoServicio } from './conciliaciones';
import { tipoArchivos } from './tipo-archivos';

// Server
export const DEV_PUERTO_SERVIDOR = (port) => {
  if (process.env.NODE_ENV === 'development') {
    return `🚀 Servicio corriendo en puerto ${chalk.bold(port)}`;
  }
  return `🚀 Servicio corriendo en puerto => ${port}`;
};
export const DEV_ENV_SERVIDOR = `🌍 Entorno de ejecución => ${chalk.bold(process.env.NODE_ENV)}`;
export const CONEXION_BD_EXITOSA = (db, host) => `🎉 Conexión exitosa a la base de datos: ${chalk.bold(db)} host: ${chalk.bold(host)}`;
export const CONEXION_DB_FALLIDA = (db, host) => `💥 Error al intentar establecer conexión con la base de datos: ${chalk.bold(db)} host: ${chalk.bold(host)}`;

export const ERROR_EJECUCION_JAR = 'Se ha producido un error durante la ejecución del archivo JAR.';
export const ERROR_SERVIDOR = 'Ha ocurrido un problema al procesar su solicitud, intente más tarde.';
export const JAR_NO_ENCONTRADO = 'No hay un proceso designado para el tipo de servicio proporcionado';
export const NOT_FOUND = 'No fue posible localizar el recurso solicitado';
export const RATE_LIMIT = 'Se ha alcanzado el límite de peticiones, intente más tarde.';
export const SERVIDOR_NO_RESPONDE = (direccion) => `No se pudo recibir respuesta del servidor: ${direccion}`;

// Sesiones
export const ERROR_AUTENTICACION = 'Credenciales inválidas. Por favor, inicia sesión de nuevo.';
export const EMPLEADO_NO_AUTORIZADO = 'Acceso denegado, comunícate con el administrador del sistema para obtener asistencia.';
export const ERROR_VALIDAR_TOKEN = 'Error al validar el token';
export const NO_TOKEN_PROVIDED = 'No se proporcionó un token';
export const TOKEN_INVALIDO = 'El token proporcionado no es válido';
export const NO_IDP = 'No se obtuvo el método de autenticación';
export const TOKEN_INVALIDO_NUMERO_EMPLEADO = 'Token inválido: No se encontró el número de empleado en el token';
export const TOKEN_INVALIDO_SCOPE = 'Token inválido: No se encontró un scope válido en el token';

// Perfiles
export const PERFIL_CONFLICTO = 'Ya existe un perfil con el nombre proporcionado, intente otro.';

// Usuarios
export const USERNAME_CONFLICTO = 'El nombre de usuario no está disponible, intente otro.';
export const CORREO_CONFLICTO = 'El correo electrónico ya está registrado, intente otro.';

// Campos
export const CAMPO_ARREGLO = 'El campo {#label} debe ser un arreglo';
export const CAMPO_ARREGLO_VACIO = 'El arreglo {#label} no debe estar vacío';
export const CAMPO_EMAIL_INVALIDO = 'El campo {#label} no es un email válido';
export const CAMPO_FUERA_DE_DEFINICION = 'El campo {#label} no forma parte de la definición';
export const CAMPO_INVALIDO = 'El valor proporcionado en el campo {#label} no es válido';
export const CAMPO_NUMERICO = 'El valor proporcionado en el campo {#label} debe ser númerico';
export const CAMPO_NUMERICO_POSITIVO = 'El valor proporcionado en el campo {#label} debe ser mayor a 0';
export const CAMPO_REQUERIDO = 'El campo {#label} es requerido';
export const CAMPOS_CARACTERES_MAXIMOS = 'El campo {#label} no debe exceder los {#limit} caracteres';
export const CAMPOS_FALTANTES = 'Debes proporcionar al menos uno de los siguientes campos: {#peers}';
export const FORMATO_INVALIDO = 'El formato del campo {#label} no es válido';
export const VALOR_BOOLEANO_INVALIDO = 'El valor proporcionado para el campo {#label} debe ser 0 o 1';

// Parametros
export const PARAMETRO_REQUERIDO = 'El parámetro {#label} es requerido';
export const PARAMETRO_INVALIDO = 'El parámetro {#label} no es válido';

// Validación de datos
export const FORMATO_FECHA = 'El formato de fecha en el campo {#label} no es válido.';
export const REGISTROS_VACIOS = 'No se encontraron registros';

export const ESTATUS_INVALIDO = 'El estatus proporcionado no es válido';
export const ESTATUS_REQUERIDO = 'El estatus es requerido';
export const ESTATUS_FORMATO_INVALIDO = 'El formato de estatus proporcionado no es válido';

export const TIPO_CONCILIACION_INVALIDO = 'El tipo de conciliación proporcionado no es válido';
export const TIPO_SERVICIO_INVALIDO = `El tipo de servicio proporcionado no es válido, los valores posibles son [${Object.values(TipoServicio).join(', ')}]`;
export const SERVICIO_NO_DISPONIBLE = `El tipo de servicio proporcionado no es válido, los valores posibles son [${[...new Set(CONCILIACIONES.map(({ tipo }) => tipo))].join(', ')}]`;

export const VALIDACION_NUMERO_ENTERO = 'El valor proporcionado debe ser un número entero';

export const TIPO_ARCHIVO_INVALIDO = `El tipo de archivo proporcionado no es válido, los valores posibles son [${Object.keys(tipoArchivos).join(', ')}]`;
export const TIPO_ARCHIVO_REQUERIDO = 'El tipo de archivo es requerido';

export const IDU_BITACORA_CONCILIACION_INVALIDO = 'El campo iduBitacoraConciliacion no es válido';
export const ERROR_REGISTRO_ACCION_CORRECCION = 'No fue posible registrar la acción o corrección, intente de nuevo';
export const ERROR_ACTUALIZACION_ACCION_CORRECCION = 'No fue posible actualizar la acción o corrección, intente de nuevo';
export const BITACORA_CONCILIACION_SIN_ARCHIVO_SALIDA = 'La bitacora no cuenta con un archivo de salida.';
export const BITACORA_CONCILIACION_ARCHIVO_NO_ENCONTRADO = 'No fue posible localizar el archivo de salida.';

export const ERROR_REGISTRO_PERFIL = 'No fue posible registrar el perfil, intente de nuevo';
export const ERROR_ACTUALIZACION_PERFIL = 'No fue posible actualizar el perfil, intente de nuevo';
export const ERROR_ELIMINAR_PERFIL = 'No fue posible eliminar el perfil, intente de nuevo';
export const PERFIL_NO_ENCONTRADO = 'El perfil solicitado no ha sido encontrado';

export const ERROR_REGISTRO_USUARIO = 'No fue posible registrar el usuario, intente de nuevo';
export const ERROR_ACTUALIZACION_USUARIO = 'No fue posible actualizar el usuario, intente de nuevo';
export const ERROR_ELIMINAR_USUARIO = 'No fue posible eliminar el usuario, intente de nuevo';
export const USUARIO_NO_ENCONTRADO = 'El usuario solicitado no ha sido encontrado';

export const ERROR_GUARDAR_ARCHIVO_MANUAL = 'Ocurrio un error al guardar el archivo. Favor de verificar';
export const ERROR_ACTUALIZACION_ARCHIVO_MANUAL = 'Ocurrio un error al actualizar el archivo. Favor de verificar';
export const ARCHIVO_MANUAL_NO_ENCONTRADO = 'No se encontró el archivo. Favor de verificar.';
export const ARCHIVO_MANUAL_SIN_RUTA = 'No se encontró la ruta del archivo. Favor de verificar.';
export const ARCHIVO_MANUAL_SIN_RUTA_JAR = 'La ruta del jar no está configurada. Favor de verificar.';
export const ARCHIVO_MANUAL_SIN_RUTA_ORIGEN = 'La ruta del archivo origen no está configurada. Favor de verificar.';
export const ARCHIVO_MANUAL_SIN_RUTA_DESTINO = 'La ruta destino del archivo. no está configurada. Favor de verificar.';
export const ARCHIVO_MANUAL_SIN_RUTA_BD = 'La ruta del archivo en el servidor de base de datos no está configurada. Favor de verificar.';
export const ARCHIVO_MANUAL_ERROR_JAR = 'Ocurrio un error al ejecutar el archivo JAR. Favor de verificar';
export const ERROR_JAR_SIN_ARCHIVO = 'El archivo no se encuentra en la ruta especificada.';
export const ERROR_PROCESO_PREVIO_EXITOSO = 'Operación denegada: ya se realizó una conciliación exitosa hoy.';

export const ERROR_REGISTRO_PLANTILLA = 'No fue posible actualizar la plantilla de correo';
export const ERROR_REGISTRO_DESTINATARIO = 'No fue posible registrar el destinatario, intente de nuevo más tarde';
export const ERROR_ELIMINAR_DESTINATARIO = 'No fue posible elimnar al destinatario, intente de nuevo más tarde';
