/* eslint-disable max-len */
export const EVENTOS_SEGURIDAD = {
  InicioSesion: 'Inicio de sesion',
  AccesoNoAutorizado: 'Intento de acceso no autorizado.',
  EjecucionManual: (id, numEmpleado) => `Ejecucion manual por empleado ${numEmpleado} de la conciliacion ${id}`,
  IntentoEjecucionManual: (id, numEmpleado) => `Intento de ejecucion manual por empleado ${numEmpleado} manual de la conciliacion ${id}`,
  UsuarioRegistrado: (numEmpleado) => `Usuario registrado por el empleado ${numEmpleado}`,
  UsuarioEliminado: (id, numEmpleado) => `Usuario ${id} eliminado por el empleado ${numEmpleado}`,
  UsuarioActualizado: (id, numEmpleado) => `Usuario ${id} actualizado por el empleado ${numEmpleado}`,
};
