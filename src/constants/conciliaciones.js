/* eslint-disable object-curly-newline */
/* eslint-disable no-multi-spaces */

/** ENUMS */
export const Periodicidad = {
  DIARIO: 'diario',
  SEMANAL: 'semanal',
};

export const TipoServicio = {
  CONCILIACION: 'conciliacion',
  PAGO_SERVICIO: 'pago_servicio',
  HOMOLOGADOS: 'homologados',
};

export const ClaveOrigen = {
  RUTA_CBANCO_ORIGEN: 'RutaCBancoOrigen',
  RUTA_CBANCO_DESTINO: 'RutaCBancoDestino',
  RUTA_CBANCO_BD: 'SUBIDA_BD_CBANCO',
  RUTA_JAR: 'RutaJar',
  RUTA_ARCHIVO: 'RutaArchivo',
};

export const ConciliacionesEnum = {
  ConciliacionCorresponsales: 1,
  Patrimonio: 2,
  Telmex: 3,
  Japac: 4,
  CFE: 5,
  ABCCapital: 6,
  Movistar: 7,
  CreditoYCasa: 8,
  Blackhawk: 9,
  Homologados: 10,
  WSConciliacionesProcesoInterno: 11,
  ConciliacionAfore: 12,
  ConciliacionABCCapital: 13,
};

export const EstatusConciliacionEnum = {
  Pendiente: 1,
  Exitoso: 2,
  Error: 3,
};

const { CONCILIACION, PAGO_SERVICIO, HOMOLOGADOS } = TipoServicio;
const { DIARIO, SEMANAL } = Periodicidad;

/** CONSTANTES */
export const TipoConciliaciones = {
  CORRESPONSALES: {
    id: 1,
    tipo: CONCILIACION,
    periodicidad: null,
    nombre: 'Conciliacion Corresponsales',
  },
  AFORE: {
    id: 12,
    tipo: CONCILIACION,
    periodicidad: null,
    nombre: 'Conciliacion Afore',
  },
  ABC_CAPITAL: {
    id: 13,
    tipo: CONCILIACION,
    periodicidad: null,
    nombre: 'Conciliacion ABC Capital',
  },
};

export const SERVICIOS_HOMOLOGADOS = {
  id: 10,
  tipo: TipoServicio.HOMOLOGADOS,
  periodicidad: null,
  nombre: 'Pago de servicios homologados - CFE',
};

export const CONCILIACIONES = [
  { id: 0,  tipo: null,          periodicidad: null,    nombre: 'Sin conciliacion' },
  { ...TipoConciliaciones.CORRESPONSALES },
  { id: 2,  tipo: PAGO_SERVICIO, periodicidad: DIARIO,  nombre: 'Pago de servicios - Patrimonio' },
  { id: 3,  tipo: PAGO_SERVICIO, periodicidad: DIARIO,  nombre: 'Pago de servicios - Telmex' },
  { id: 4,  tipo: PAGO_SERVICIO, periodicidad: DIARIO,  nombre: 'Pago de servicios - Japac' },
  { id: 5,  tipo: PAGO_SERVICIO, periodicidad: DIARIO,  nombre: 'Pago de servicios - CFE' },
  { id: 6,  tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Pago de servicios - ABC Capital' },
  { id: 7,  tipo: PAGO_SERVICIO, periodicidad: DIARIO,  nombre: 'Pago de servicios - Movistar' },
  { id: 8,  tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Pago de servicios - Credito y Casa' },
  { id: 9,  tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Pago de servicios - Blackhawk' },
  { ...SERVICIOS_HOMOLOGADOS },
  { id: 11, tipo: HOMOLOGADOS,   periodicidad: null,    nombre: 'WS Conciliaciones Proceso Interno' },
  { ...TipoConciliaciones.AFORE },
  { ...TipoConciliaciones.ABC_CAPITAL },
  { id: 14, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Liquidacion pago de servicios - CFE' },
  { id: 15, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Reporte Liquidacion Japac' },
  { id: 16, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Reporte de liquidacion - Patrimonio semanal' },
  { id: 17, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Pago de servicios semanales - Telmex' },
  { id: 18, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Reporte Liquidacion - OPERAX' },
  { id: 19, tipo: PAGO_SERVICIO, periodicidad: SEMANAL, nombre: 'Reporte Liquidacion - Movistar' },
];
