import sql from 'mssql';

import { log } from '../utilities/logger';
import { ObtenerCredencialesBd } from '../utilities/obtener-credenciales-bd';
import { CONEXION_BD_EXITOSA, CONEXION_DB_FALLIDA } from '../constants';

const DEV = process.env.NODE_ENV === 'development';
const LOG_QUERY = !!Number(process.env.LOG_QUERY);

let config;

/**
 * Función que establece conexión con la base de datos
 */
async function establecerConexion() {
  const { CATEGORIA_PERSONAL: categoria } = process.env;
  let database; let
    server;
  try {
    const credenciales = await ObtenerCredencialesBd(categoria) || {};
    database = credenciales.db;
    server = credenciales.host;

    delete credenciales.db;
    delete credenciales.host;

    config = {
      ...credenciales,
      database,
      server,
      pool: {
        max: 10,
        min: 0,
        idleTimeoutMillis: 30000,
      },
      options: {
        encrypt: false,
        trustServerCertificate: true,
      },
    };

    sql.on('error', () => {
      logger.error(CONEXION_DB_FALLIDA(database));
      process.exit(1);
    });

    await sql.connect(config);
    logger.log({ level: 'server', message: CONEXION_BD_EXITOSA(database, server) });
  } catch (error) {
    logger.log({ level: 'error', message: `${CONEXION_DB_FALLIDA(database, server)}\n${error.message}` });
    process.exit(1);
  }
}

establecerConexion();

/**
 * Método para ejecutar una query de forma segura
 * @param {String} query Query a ejecutar
 *
 * @returns {Promise<import('mssql').IResult<any>>} Resultado de la query
 */
export const query = async (query) => {
  const pool = await sql.connect(config);
  const start = Date.now();
  const res = await pool.query(query);

    if (DEV && LOG_QUERY) {
      const duracion = Date.now() - start;

      log({
        isError: false,
        title: 'Consulta ejecutada',
        descripcion: query,
        objeto: { duracion: duracion / 1000, rows: res.rowsAffected[0] },
      });
    }

    return res;
};

/**
 * Método para ejecutar una query de forma segura
 * @param {String} sp Stored procedure a ejecutar
 * @param {{[paramName: String]: any}} [inputParams] Parámetros de entrada
 * @param {{[paramName: String]: import('mssql').ISqlType}} [outputParams] Parámetros de salida
 *
 * @returns {Promise<import('mssql').IResult<any>>} Resultado de la query
 */
export const execute = async (sp, inputParams = {}, outputParams = {}) => {
  const pool = await sql.connect(config);
  const start = Date.now();
  const request = pool.request();

  if (inputParams) {
    for (const paramName in inputParams) {
      if (Object.prototype.hasOwnProperty.call(inputParams, paramName)) {
        request.input(paramName, inputParams[paramName]);
      }
    }
  }

  if (outputParams) {
    for (const paramName in outputParams) {
      if (Object.prototype.hasOwnProperty.call(outputParams, paramName)) {
        request.output(paramName, outputParams[paramName]);
      }
    }
  }

  const res = await request.execute(sp);

  if (DEV && LOG_QUERY) {
    const duracion = Date.now() - start;
    const formatParams = [];

    for (const paramName in inputParams) {
      if (Object.prototype.hasOwnProperty.call(inputParams, paramName)) {
        const valor = typeof inputParams[paramName] === 'string'
          ? `'${inputParams[paramName]}'`
          : inputParams[paramName];
        formatParams.push(`${valor}`);
      }
    }

    log({
      isError: false,
      title: 'Procedimiento ejecutado',
      descripcion: `EXEC ${sp} ${formatParams.join(', ')}`,
      objeto: { salida: `@${Object.keys(outputParams).join(', @')}`, duracion: duracion / 1000, rows: res.rowsAffected[0] },
    });
  }

  return res;
};
