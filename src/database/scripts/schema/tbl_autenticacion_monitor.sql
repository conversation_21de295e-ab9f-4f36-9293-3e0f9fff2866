CREATE TABLE cat_perfil_monitores (
    idu_perfil SERIAL PRIMARY KEY,
    nom_perfil VARCHAR(50) NOT NULL DEFAULT '',
    des_perfil TEXT NOT NULL DEFAULT '',
    opc_estatus BOOLEAN NOT NULL DEFAULT TRUE,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE cat_usuario_monitores (
    idu_usuario SERIAL PRIMARY KEY,
    idu_perfil INT NOT NULL DEFAULT 0,
    num_empleado INT NOT NULL DEFAULT 0,
    nom_empleado TEXT NOT NULL DEFAULT '',
    nom_puesto TEXT NOT NULL DEFAULT '',
    num_telefono VARCHAR(10) NOT NULL DEFAULT '',
    opc_estatus BOOLEAN NOT NULL DEFAULT TRUE,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_cat_usuario_monitores_cat_perfil_monitores FOREIGN KEY (idu_perfil) REFERENCES cat_perfil_monitores (idu_perfil) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE cat_modulo_monitores (
    idu_modulo SERIAL PRIMARY KEY,
    nom_modulo VARCHAR(50) NOT NULL DEFAULT '',
    des_modulo TEXT NOT NULL DEFAULT '',
    des_ruta TEXT NOT NULL DEFAULT '',
    idu_modulo_padre INT NOT NULL DEFAULT 0,
    opc_estatus BOOLEAN NOT NULL DEFAULT TRUE,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_cat_modulo_monitores_cat_modulo_monitores FOREIGN KEY (idu_modulo_padre) REFERENCES cat_modulo_monitores (idu_modulo) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE cat_permiso_monitores (
    idu_perfil INT NOT NULL DEFAULT 0,
    idu_modulo INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_permiso_monitores PRIMARY KEY (idu_perfil, idu_modulo),
    CONSTRAINT fk_cat_permiso_monitores_cat_perfil_monitores FOREIGN KEY (idu_perfil) REFERENCES cat_perfil_monitores (idu_perfil) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_permiso_monitores_cat_modulo_monitores FOREIGN KEY (idu_modulo) REFERENCES cat_modulo_monitores (idu_modulo) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);
