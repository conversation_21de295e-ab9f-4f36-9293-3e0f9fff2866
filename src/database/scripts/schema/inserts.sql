-- ----------------------------
-- <PERSON><PERSON><PERSON><PERSON> del sistema
-- ----------------------------
BEGIN;
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (0, 'Raiz', 'Este modulo indica la ausencia de modulo padre.', '', 0, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (1, 'Accesos', 'Modulo para administrar los accesos al sistema.', '', 0, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (2, 'Usuarios', 'Modulo para administrar los usuarios.', '/usuarios', 1, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (3, 'Perfiles', 'Modulo para administrar los perfiles.', '/perfiles', 1, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (4, 'Monitores', 'Modulo para visualizar la informacion generada por las conciliaciones.', '', 0, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (5, 'Conciliacion Corresponsales', 'Modulo para visualizar la informacion de las conciliaciones de corresponsales.', '/corresponsales', 4, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (6, 'Pagos de servicios', 'Modulo para visualizar la informacion de pagos de servicios.', '/servicios', 4, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (7, 'Servicios homologados', 'Modulo para visualizar la informacion de servicios homologados.', '/homologados', 4, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (8, 'Envio manual', 'Modulo para la interaccion de envio manual.', '', 0, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (9, 'Archivo manual en caso de fallo', 'Modulo para la interaccion con archivos manual en caso de fallo.', '/archivo-manual-fallo', 8, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (10, 'Conciliacion Afore', 'Modulo para visualizar la informacion de las conciliaciones de Afore', '/afore', 4, 't', 't');
INSERT INTO "public"."cat_modulo_monitores" ("idu_modulo", "nom_modulo", "des_modulo", "des_ruta", "idu_modulo_padre", "opc_estatus", "opc_activo") VALUES (11, 'Conciliacion ABC Capital', 'Modulo para visualizar la informacion de las conciliaciones de ABC Capital.', '/capital', 4, 't', 't');
COMMIT;

-- ----------------------------
-- Perfil de administrador
-- ----------------------------
BEGIN;
INSERT INTO "public"."cat_perfil_monitores" ("nom_perfil", "des_perfil", "opc_estatus", "opc_activo") VALUES ('Administrador', 'Perfil de administrador', 't', 't');
COMMIT;

-- ----------------------------
-- Permisos al administrador
-- ----------------------------
BEGIN;
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 2, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 3, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 5, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 6, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 7, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 9, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 10, 't');
INSERT INTO "public"."cat_permiso_monitores" ("idu_perfil", "idu_modulo", "opc_activo") VALUES (1, 11, 't');
COMMIT;

-- ----------------------------
-- Usuario inicial
-- ----------------------------
BEGIN;
-- PROD
INSERT INTO "public"."cat_usuario_monitores" ("idu_perfil", "num_empleado", "nom_empleado", "nom_puesto", "num_telefono", "opc_estatus", "opc_activo") VALUES (1, 96274379, 'CANDELARIA VALE AGUILERA', 'OPERADOR DE SISTEMAS', '----------', 't', 't');

-- DEV
INSERT INTO "public"."cat_usuario_monitores" ("idu_perfil", "num_empleado", "nom_empleado", "nom_puesto", "num_telefono", "opc_estatus", "opc_activo") VALUES (1, 90248458, 'JESUS ABRAHAM CEJA CHICUATE', 'PROGRAMADOR', '----------', 't', 't');
COMMIT;
