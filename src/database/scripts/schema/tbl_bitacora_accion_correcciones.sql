CREATE TABLE his_bitacora_accion_correcciones (
    idu_bitacora_accion_correccion SERIAL PRIMARY KEY,
    idu_bitacora_conciliacion INT NOT NULL DEFAULT 0,
    idu_estatus_conciliacion INT NOT NULL DEFAULT 0,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    nom_persona_inspeccion TEXT NOT NULL DEFAULT '',
    des_observacion TEXT NOT NULL DEFAULT '',
    CONSTRAINT fk_his_bitacora_accion_correcioness_his_bitacora_conciliaciones FOREIGN KEY (idu_bitacora_conciliacion) REFERENCES his_bitacora_conciliaciones (idu_bitacora_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_his_bitacora_accion_correcioness_cat_estatus_conciliaciones FOREIGN KEY (idu_estatus_conciliacion) REFERENCES cat_estatus_conciliaciones (idu_estatus_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);
