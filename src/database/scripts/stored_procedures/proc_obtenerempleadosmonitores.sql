CREATE OR ALTER PROCEDURE [dbo].[proc_obtenerempleadosmonitores]
    @pBusqueda varchar(250),
    @pLimit integer,
    @pOffset integer,
    @total integer OUTPUT,
    @registros nvarchar(max) OUTPUT
AS
BEGIN

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 31/10/2023
-- Descripción General: Este procedimiento sirve para obtener los empleados
-- del disponibles para asignarles un usuario en la aplicación de Monitores
-- de Conciliación
-- ========================================================================

    DECLARE @count INT

    SELECT @count = COUNT(*) FROM sapCatalogoEmpleados sce INNER JOIN sapCatalogoPuestos scp ON sce.NumeroPuesto = scp.Numero WHERE sce.Cancelado = 0 AND (TRIM(sce.Nombre) + ' ' + TRIM(sce.ApellidoPaterno) + ' ' + TRIM(sce.ApellidoMaterno)) LIKE '%' + @pBusqueda + '%'
	SET @total = @count

	SELECT @registros = (
		SELECT
			sce.Numemp AS idu_empleado,
			LTRIM(RTRIM(LTRIM(RTRIM(sce.Nombre)) + ' ' + LTRIM(RTRIM(sce.ApellidoPaterno)) + ' ' + LTRIM(RTRIM(sce.ApellidoMaterno)))) AS nombre,
			LTRIM(RTRIM(scp.Nombre)) AS puesto,
			LTRIM(RTRIM(sce.Telefono)) AS telefono
		FROM sapCatalogoEmpleados sce INNER JOIN sapCatalogoPuestos scp ON sce.NumeroPuesto = scp.Numero
		WHERE sce.Cancelado = 0 AND (TRIM(sce.Nombre) + ' ' + TRIM(sce.ApellidoPaterno) + ' ' + TRIM(sce.ApellidoMaterno)) LIKE '%' + @pBusqueda + '%'
		ORDER BY sce.Numemp
		OFFSET @pOffset ROWS
		FETCH NEXT @pLimit ROWS ONLY FOR JSON AUTO
	)
END
