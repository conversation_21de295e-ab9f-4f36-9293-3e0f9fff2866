CREATE OR REPLACE FUNCTION fun_obtenerusuariomonitores (p_idu_usuario INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 01/11/2023
-- Descripción General: Esta función sirve para obtener un usuario por
-- su identificador.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
		SELECT
		  JSON_BUILD_OBJECT(
			'idu_usuario', cum.idu_usuario,
			'num_empleado', cum.num_empleado,
			'idu_perfil', cpm.idu_perfil,
			'nom_perfil', cpm.nom_perfil,
			'nom_empleado', cum.nom_empleado,
			'nom_puesto', cum.nom_puesto,
			'num_telefono', cum.num_telefono,
			'opc_estatus', cum.opc_estatus,
			'modulos', (SELECT COALESCE(JSON_AGG(cmm.nom_modulo), '[]') FROM cat_permiso_monitores cpm INNER JOIN cat_modulo_monitores cmm ON cpm.idu_modulo = cmm.idu_modulo WHERE cpm.idu_perfil = cum.idu_perfil)
		) as resultado
		FROM
		  cat_usuario_monitores cum INNER JOIN cat_perfil_monitores cpm ON cum.idu_perfil = cpm.idu_perfil
		WHERE
		  cum.opc_activo
		  AND cpm.opc_activo
		  AND cpm.opc_estatus
		  AND cum.idu_usuario = p_idu_usuario
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerusuariomonitores(p_idu_usuario INT) IS 'Esta función sirve para obtener un usuario por su identificador,
p_idu_usuario= Identificador del usuario a obtener';