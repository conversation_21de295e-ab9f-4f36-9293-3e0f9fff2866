DROP FUNCTION IF EXISTS fun_obtenerregistrospendientes(INT, INT, DATE);
CREATE OR REPLACE FUNCTION fun_obtenerregistrospendientes(pagina INT, numRegistros INT, fechaFiltro DATE)
RETURNS TABLE (
   fecha       DATE,
   clave       CHAR(1),
   movimientos BIGINT,
   importe     BIGINT,
	count 	   BIGINT
)
LANGUAGE plpgsql AS
$$
BEGIN
   RETURN QUERY
   SELECT a.fecha, a.clave, count (1) AS movimientos, sum (a.importe), COUNT(1) OVER() AS count
   FROM tiecarmovhistorial a
   LEFT JOIN ctl_cbanco_archivo_detalles cad ON (a.rpu = cad.clv_rpu)
   WHERE
      (
         a.fecha <= COALESCE(fechaFiltro, (NOW() - INTERVAL '7 days'))::DATE
         AND a.fecha >= COALESCE(fechaFiltro, (NOW() - INTERVAL '40 days'))::DATE
      )
      AND a.clave = 'W'
      AND fol_sucursal != 0
      AND cad.clv_rpu IS NULL
   GROUP BY a.clave, a.fecha ORDER BY a.fecha, a.clave
   LIMIT numRegistros OFFSET (pagina - 1) * numRegistros;
END
$$;