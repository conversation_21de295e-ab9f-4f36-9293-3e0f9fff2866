CREATE OR REPLACE FUNCTION fun_obtenerestadisticageneralabccapital(p_fecha DATE)

-- ================================================================================================================================================
-- Autor: Gaman Solutions
-- Fecha: 16/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- generales de la conciliación de ABC Capital.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
	registros_iniciales INT := 0;
	registros_conciliados INT := 0;
	registros_coppel_no_cyc INT := 0;
	registros_cyc_no_coppel INT := 0;
BEGIN
	SELECT
		COUNT(*) AS registros_iniciales,
		COALESCE(SUM(CASE WHEN mcac.idu_estatus_conciliacion = 2 THEN 1 ELSE 0 END), 0) AS registros_conciliados
	INTO
        registros_iniciales,
        registros_conciliados
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_abc_capital mcac ON hbc.idu_bitacora_conciliacion = mcac.idu_bitacora_conciliacion
	WHERE hbc.fec_conciliacion = p_fecha;

	SELECT
		COALESCE(SUM(opc_coppel_si_cyc_no), 0) AS registros_coppel_no_cyc,
		COALESCE(SUM(opc_coppel_no_cyc_si), 0) AS registros_cyc_no_coppel
	INTO
		registros_coppel_no_cyc,
		registros_cyc_no_coppel
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_diferencias_conciliacion_abc_capital mdcac ON hbc.idu_bitacora_conciliacion = mdcac.idu_bitacora_conciliacion
	WHERE hbc.fec_conciliacion = p_fecha;

	RETURN JSON_BUILD_OBJECT(
		'registros_iniciales', registros_iniciales,
		'registros_conciliados', registros_conciliados,
		'registros_coppel_no_cyc', registros_coppel_no_cyc,
		'registros_cyc_no_coppel', registros_cyc_no_coppel
	);

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener las estadísticas generales de conciliación ABC Capital: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadisticageneralabccapital(p_fecha DATE) IS 'Esta función sirve para obtener las estadísticas generales de la conciliación de ABC Capital.
p_fecha= Fecha a filtrar la información.';
