CREATE OR REPLACE FUNCTION fun_obtenerdetallesbitacorasconciliaciones(
    p_idu_bitacora_conciliacion INT,
    p_idu_estatus_conciliacion  INT DEFAULT NULL,
    p_busqueda TEXT DEFAULT NULL,
    p_limit INT DEFAULT 10,
    p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/11/2023
-- Descripción General: Esta función sirve para obtener los detalles de una
-- bitacora de conciliación.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN JSON_BUILD_OBJECT(
		'total',
		(
            SELECT COUNT(*)
            FROM his_bitacora_conciliacion_detalles hbcd
                INNER JOIN his_bitacora_conciliaciones hbc ON hbcd.idu_bitacora_conciliacion = hbc.idu_bitacora_conciliacion
                INNER JOIN cat_conciliaciones cc ON hbc.idu_conciliacion = cc.idu_conciliacion
                INNER JOIN cat_estatus_conciliaciones cec ON hbcd.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
            WHERE
                hbc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
                AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN hbcd.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
                AND CASE WHEN p_busqueda != '' THEN hbcd.des_log ILIKE '%' || p_busqueda || '%' ELSE TRUE END
        ),
		'registros',
		COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
			'fecha', fecha,
			'conciliacion', conciliacion,
			'descripcion', descripcion,
			'estatus', estatus
		)), '[]')
	) FROM (
    SELECT
      hbcd.fec_registro::TEXT as fecha,
      cc.nom_conciliacion as conciliacion,
      hbcd.des_log as descripcion,
      cec.des_descripcion as estatus
    FROM his_bitacora_conciliacion_detalles hbcd
      INNER JOIN his_bitacora_conciliaciones hbc ON hbcd.idu_bitacora_conciliacion = hbc.idu_bitacora_conciliacion
      INNER JOIN cat_conciliaciones cc ON hbc.idu_conciliacion = cc.idu_conciliacion
      INNER JOIN cat_estatus_conciliaciones cec ON hbcd.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
    WHERE
      hbc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
      AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN hbcd.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
      AND CASE WHEN p_busqueda != '' THEN hbcd.des_log ILIKE '%' || p_busqueda || '%' ELSE TRUE END
    ORDER BY hbcd.fec_registro ASC
    LIMIT p_limit
    OFFSET p_offset
	) consulta;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerdetallesbitacorasconciliaciones(
    p_idu_bitacora_conciliacion INT,
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_limit INT,
	p_offset INT
) IS 'Esta función sirve para obtener los detalles de una bitacora de conciliación.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener.
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener.
p_limit= Límite de registros a obtener.
p_offset= Registro inicial a obtener.';
