DROP FUNCTION IF EXISTS fun_obtenerbloqueosporconciliacionfecha(INT, DATE);
CREATE OR REPLACE FUNCTION fun_obtenerbloqueosporconciliacionfecha(iduConciliacion INT, fecha DATE)
RETURNS TABLE (
    idu_conciliacion_bloqueo	INTEGER,
    idu_conciliacion	        INTEGER,
    des_mensaje_bitacora        TEXT,
    opc_estatus                 BIT,
    fec_bloqueo     		    TIMESTAMP,
    fec_registro     		    TIMESTAMP,
    idu_usuario_registro        TEXT,
    nom_empleado                TEXT,
    idu_estatus_conciliacion	INTEGER,
    des_estatus                 TEXT,
    count       		 		BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
    RETURN QUERY
    SELECT 
        cb.idu_conciliacion_bloqueo,
        cb.idu_conciliacion,
	    cb.des_mensaje_bitacora,
        cb.opc_estatus,
        cb.fec_bloqueo,
        cb.fec_registro,
        cb.idu_usuario_registro,
        um.nom_empleado,
        cb.idu_estatus_conciliacion,
        ec.des_descripcion,
        COUNT(*) OVER() AS count
    FROM cat_conciliacion_bloqueos cb
    INNER JOIN cat_usuario_monitores um ON (cb.idu_usuario_registro = um.idu_usuario::TEXT)
    INNER JOIN cat_estatus_conciliaciones ec ON (cb.idu_estatus_conciliacion = ec.idu_estatus_conciliacion)
	    WHERE cb.idu_conciliacion = iduConciliacion
            AND cb.fec_bloqueo = fecha
            AND cb.opc_estatus = b'1';
	 
END
$func$;