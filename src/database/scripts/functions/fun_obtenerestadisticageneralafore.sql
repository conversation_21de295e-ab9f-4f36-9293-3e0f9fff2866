CREATE OR REPLACE FUNCTION fun_obtenerestadisticageneralafore(p_fecha DATE)

-- ================================================================================================================================================
-- Autor: Gaman Solutions
-- Fecha: 16/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- generales de la conciliación de Afore.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
	registros_iniciales INT := 0;
	registros_conciliados INT := 0;
	registros_afore_no_coppel INT := 0;
	registros_coppel_no_afore INT := 0;
BEGIN
	SELECT
        COUNT(*) AS registros_iniciales,
        SUM(CASE WHEN mca.opc_estatus = 2 THEN 1 ELSE 0 END) AS registros_conciliados
    INTO
        registros_iniciales,
        registros_conciliados
    FROM his_bitacora_conciliaciones hbc
        INNER JOIN mov_conciliacion_afore mca ON hbc.idu_bitacora_conciliacion = mca.idu_bitacora_conciliacion
    WHERE hbc.fec_conciliacion = p_fecha;

	registros_afore_no_coppel = COUNT(*) AS registros_coppel_no_afore
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_afore mca ON hbc.idu_bitacora_conciliacion = mca.idu_bitacora_conciliacion
	WHERE mca.opc_estatus = 3 AND hbc.fec_conciliacion = p_fecha;

    registros_coppel_no_afore = COUNT(*) AS registros_afore_no_coppel
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_coppel_afore mcca ON hbc.idu_bitacora_conciliacion = mcca.idu_bitacora_conciliacion
	WHERE mcca.opc_estatus = 3 AND hbc.fec_conciliacion = p_fecha;

	RETURN JSON_BUILD_OBJECT(
		'registros_iniciales', registros_iniciales,
		'registros_conciliados', registros_conciliados,
		'registros_coppel_no_afore', registros_coppel_no_afore,
		'registros_afore_no_coppel', registros_afore_no_coppel
	);

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener las estadísticas generales de conciliación Afore: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadisticageneralafore(p_fecha DATE) IS 'Esta función sirve para obtener las estadísticas generales de la conciliación de Afore.
p_fecha= Fecha a filtrar la información.';
