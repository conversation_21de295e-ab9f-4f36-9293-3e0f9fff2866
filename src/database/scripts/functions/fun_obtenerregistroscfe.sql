CREATE OR REPLACE FUNCTION fun_obtenerregistroscfe(pagina INT, numRegistros INT)
RETURNS TABLE (
   tienda      CHAR(3),
   fecha       DATE,
   caja        CHAR(2),
   contrato    CHAR(21),
   importe     INTEGER,
   recibo      INTEGER,
   count       BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
   RETURN QUERY
   SELECT  cfe.tienda, cfe.fecha, cfe.caja, cfe.contrato, cfe.importe, cfe.recibo, COUNT(*) OVER() AS count
   FROM sicfemovimientos cfe LIMIT numRegistros OFFSET (pagina - 1) * numRegistros;
	 
END
$func$;