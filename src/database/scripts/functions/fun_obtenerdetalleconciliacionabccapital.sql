CREATE OR REPLACE FUNCTION fun_obtenerdetalleconciliacionabccapital(
    p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_idu_estatus_conciliacion INT DEFAULT NULL,
	p_busqueda TEXT DEFAULT NULL,
	p_tienda VARCHAR(4) DEFAULT NULL,
	p_limit INT DEFAULT 10,
	p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- de la conciliación de ABC Capital.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
		SELECT
			JSON_BUILD_OBJECT(
				'total',
				(
					SELECT COUNT(*)
					FROM mov_conciliacion_abc_capital mcac INNER JOIN cat_estatus_conciliaciones cec
						ON mcac.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
                    WHERE mcac.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
                        AND mcac.fec_movimiento::DATE = p_fecha
                        AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mcac.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
                        AND CASE WHEN p_busqueda != '' THEN (
                            num_monto_pago::TEXT ILIKE '%' || p_busqueda || '%'
                            OR des_area::TEXT ILIKE '%' || p_busqueda || '%'
                            OR des_referencia::TEXT ILIKE '%' || p_busqueda || '%'
                            OR num_recibo::TEXT ILIKE '%' || p_busqueda || '%'
                            OR des_portafolio::TEXT ILIKE '%' || p_busqueda || '%'
                        ) ELSE TRUE END
                        AND CASE WHEN p_tienda != '' THEN num_tienda::VARCHAR(4) = p_tienda ELSE TRUE END
				),
				'registros',
				COALESCE(JSON_AGG (
					JSON_BUILD_OBJECT (
						'num_consecutivo', num_consecutivo::INT,
						'referencia',      referencia,
						'fecha_pago',      fecha_pago::TEXT,
						'monto_pagado',    monto_pagado,
						'num_tienda',      num_tienda,
						'area',            area,
						'num_caja',        num_caja::INT,
						'num_secuencia',   num_secuencia,
						'num_recibo',      num_recibo,
						'empresa',         empresa,
						'portafolio',      portafolio,
                        'tienda_replicada', (SELECT COUNT(*) FROM tiecontroldemovimientosadministracion WHERE tienda = num_tienda AND fecha = p_fecha AND replicado = 2) > 0,
						'estatus',         estatus
					)
				), '[]'),
                'archivo',
                (SELECT des_archivo FROM mov_conciliacion_abc_capital WHERE idu_bitacora_conciliacion = p_idu_bitacora_conciliacion LIMIT 1)
		    )
		FROM (
			SELECT
				TRIM(num_consecutivo) AS num_consecutivo,
				TRIM(des_referencia) AS referencia,
				fec_fecha_pago AS fecha_pago,
				num_monto_pago AS monto_pagado,
				num_tienda AS num_tienda,
				TRIM(des_area) AS area,
				TRIM(num_caja) AS num_caja,
				num_secuencia AS num_secuencia,
				num_recibo AS num_recibo,
				TRIM(des_empresa) AS empresa,
				TRIM(des_portafolio) AS portafolio,
				cec.des_descripcion AS estatus
			FROM mov_conciliacion_abc_capital mcac
				INNER JOIN cat_estatus_conciliaciones cec ON mcac.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
			WHERE mcac.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
				AND mcac.fec_movimiento::DATE = p_fecha
				AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mcac.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
				AND CASE WHEN p_busqueda != '' THEN (
					num_monto_pago::TEXT ILIKE '%' || p_busqueda || '%'
					OR des_area::TEXT ILIKE '%' || p_busqueda || '%'
					OR des_referencia::TEXT ILIKE '%' || p_busqueda || '%'
					OR num_recibo::TEXT ILIKE '%' || p_busqueda || '%'
					OR des_portafolio::TEXT ILIKE '%' || p_busqueda || '%'
                    OR num_secuencia::TEXT ILIKE '%' || p_busqueda || '%'
				) ELSE TRUE END
				AND CASE WHEN p_tienda != '' THEN num_tienda::VARCHAR(4) = p_tienda ELSE TRUE END
			ORDER BY idu_conciliacion_abc_capital
			LIMIT p_limit
			OFFSET p_offset
		) consulta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerdetalleconciliacionabccapital(
    p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_tienda VARCHAR,
	p_limit INT,
	p_offset INT
) IS 'Esta función sirve para obtener las estadísticas de la conciliación de ABC Capital.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener.
p_fecha= Fecha de la bitácora de conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener.
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener.
p_tienda= Tienda a buscar en la bitácora de conciliación a obtener.
p_limit= Límite de registros a obtener.
p_offset= Registro inicial a obtener.';
