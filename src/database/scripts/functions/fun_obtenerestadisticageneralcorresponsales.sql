CREATE OR REPLACE FUNCTION fun_obtenerestadisticageneralcorresponsales(p_fecha DATE)

-- ================================================================================================================================================
-- Autor: Gaman Solutions
-- Fecha: 17/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- generales de la conciliación de Corresponsales.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN JSON_BUILD_OBJECT(
		'depositos_retiros_tarjetas_debito',
		COALESCE(SUM(CASE WHEN mcc.des_archivo = 'CCP' THEN 1 ELSE 0 END) - SUM(CASE WHEN mcc.des_archivo = 'CCP' AND mcc.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END), 0),
		'depositos_retiros_tarjetas_credito',
		COALESCE(SUM(CASE WHEN mcc.des_archivo = 'CCD' THEN 1 ELSE 0 END) - SUM(CASE WHEN mcc.des_archivo = 'CCD' AND mcc.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END), 0),
		'movimientos_transferencias_bancoppel',
		COALESCE(SUM(CASE WHEN mcc.des_archivo = 'TPD' THEN 1 ELSE 0 END) - SUM(CASE WHEN mcc.des_archivo = 'TPD' AND mcc.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END), 0),
		'diferencias',
		COALESCE(SUM(CASE WHEN mcc.des_archivo = 'DIF' THEN 1 ELSE 0 END) - SUM(CASE WHEN mcc.des_archivo = 'DIF' AND mcc.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END), 0)
	)
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_corresponsales mcc ON hbc.idu_bitacora_conciliacion = mcc.idu_bitacora_conciliacion
	WHERE hbc.fec_conciliacion = p_fecha;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener las estadísticas generales de conciliación Corresponsales: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadisticageneralcorresponsales(p_fecha DATE) IS 'Esta función sirve para obtener las estadísticas generales de la conciliación de Corresponsales.
p_fecha= Fecha a filtrar la información.';
