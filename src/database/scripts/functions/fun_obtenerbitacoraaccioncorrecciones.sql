CREATE OR REPLACE FUNCTION fun_obtenerbitacoraaccioncorrecciones(
	p_idu_bitacora_conciliacion INT,
	p_idu_estatus_conciliacion INT DEFAULT NULL,
	p_busqueda TEXT DEFAULT '',
	p_limit INT DEFAULT 10,
	p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/11/2023
-- Descripción General: Esta función sirve para obtener las bitacoras de la
-- acción y corrección.
-- ========================================================================

  RETURNS JSON AS
$BODY$
BEGIN
	RETURN JSON_BUILD_OBJECT(
		'total',
		(
			SELECT COUNT(*)
			FROM his_bitacora_accion_correcciones hbac
        INNER JOIN his_bitacora_conciliaciones hbc ON hbac.idu_bitacora_conciliacion = hbc.idu_bitacora_conciliacion
        INNER JOIN cat_estatus_conciliaciones cec ON hbac.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
			WHERe hbc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
				AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN hbac.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
			  AND CASE WHEN p_busqueda != '' THEN hbac.des_observacion ILIKE '%' || p_busqueda || '%' ELSE TRUE END
		),
		'registros',
		COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
			'idu_bitacora_accion_correccion', idu_bitacora_accion_correccion,
			'fecha_registro', fecha_registro,
			'fecha_actualizacion', fecha_actualizacion,
			'persona_inspeccion', persona_inspeccion,
			'observacion', observacion,
			'estatus', estatus
		)), '[]')
	) FROM (
		SELECT
			hbac.idu_bitacora_accion_correccion,
			hbac.fec_registro as fecha_registro,
			hbac.fec_actualizacion as fecha_actualizacion,
			hbac.nom_persona_inspeccion as persona_inspeccion,
			hbac.des_observacion as observacion,
			cec.des_descripcion as estatus
		FROM his_bitacora_accion_correcciones hbac
			INNER JOIN his_bitacora_conciliaciones hbc ON hbac.idu_bitacora_conciliacion = hbc.idu_bitacora_conciliacion
			INNER JOIN cat_estatus_conciliaciones cec ON hbac.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
		WHERE
			hbc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
			AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN hbac.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
			AND CASE WHEN p_busqueda != '' THEN hbac.des_observacion ILIKE '%' || p_busqueda || '%' ELSE TRUE END
		LIMIT p_limit
		OFFSET p_offset
	) consutla;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerbitacoraaccioncorrecciones (
	p_idu_bitacora_conciliacion INT,
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_limit INT,
	p_offset INT
) IS 'Esta función sirve para obtener las bitacoras de la acción y corrección.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener.
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener.
p_limit= Límite de registros a obtener.
p_offset= Registro inicial a obtener.';
