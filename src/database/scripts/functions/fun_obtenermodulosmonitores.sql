CREATE OR REPLACE FUNCTION fun_obtenermodulosmonitores()

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 27/10/2023
-- Descripción General: Esta función obtiene todos los módulos activos
-- disponibles para los monitores.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
  RETURN (
    SELECT JSON_AGG(
      JSON_BUILD_OBJECT(
        'idu_modulo', cmm.idu_modulo,
        'nombre', cmm.nom_modulo,
        'descripcion', cmm.des_modulo,
        'ruta', cmm.des_ruta,
        'idu_modulo_padre', cmm.idu_modulo_padre,
        'estatus', cmm.opc_estatus,
        'procesos', (
            SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
                'idu_conciliacion', p.idu_conciliacion,
                'idu_modulo', idu_modulo,
                'nom_conciliacion', c.nom_conciliacion
                )
            ), '[]')
            FROM cat_modulo_proceso_ejecucion_manual_monitores p
            INNER JOIN cat_conciliaciones c ON p.idu_conciliacion = c.idu_conciliacion
            WHERE idu_modulo = cmm.idu_modulo AND opc_activo = TRUE
        )
      )
    ) as resultado
    FROM
      cat_modulo_monitores cmm
    WHERE
      cmm.opc_estatus AND cmm.idu_modulo > 0
  );
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenermodulosmonitores() IS 'Esta función obtiene todos los módulos activos disponibles para los monitores.';
