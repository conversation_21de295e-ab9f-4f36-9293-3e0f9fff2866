CREATE OR REPLACE FUNCTION fun_obtenerusuariosmonitores(
  p_busqueda TEXT DEFAULT '',
  p_limit INT DEFAULT 10,
  p_offset INT DEFAULT 0,
  p_estatus BOOLEAN DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función sirve para obtener todos los usuarios
-- activos registrados para los monitores de conciliación páginados.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
    SELECT
      JSON_BUILD_OBJECT (
				'total',
				(
          SELECT COUNT ( * ) FROM cat_usuario_monitores
          WHERE opc_activo
            AND CASE WHEN p_busqueda != '' THEN nom_empleado ILIKE '%' || p_busqueda || '%' ELSE TRUE END
            AND CASE WHEN p_estatus IS NOT NULL THEN opc_estatus = p_estatus ELSE TRUE END
        ),
				'registros',
				JSON_AGG (
					JSON_BUILD_OBJECT (
						'idu_usuario',
						consulta.idu_usuario,
            'num_empleado',
            consulta.num_empleado,
						'nom_empleado',
						consulta.nom_empleado,
            'perfil',
            JSON_BUILD_OBJECT (
              'idu_perfil',
              consulta.idu_perfil,
              'nom_perfil',
              consulta.nom_perfil
            ),
						'nom_puesto',
						consulta.nom_puesto,
            'num_telefono',
            consulta.num_telefono,
						'opc_estatus',
						consulta.opc_estatus
					)
				)
			)
    FROM (
      SELECT
        cum.idu_usuario,
        cum.num_empleado,
        cum.nom_empleado,
        cpm.idu_perfil,
        cpm.nom_perfil,
        cum.nom_puesto,
        cum.num_telefono,
        cum.opc_estatus
      FROM
        cat_usuario_monitores cum INNER JOIN cat_perfil_monitores cpm ON cum.idu_perfil = cpm.idu_perfil
      WHERE cum.opc_activo = TRUE
      AND CASE WHEN p_busqueda != '' THEN cum.nom_empleado ILIKE '%' || p_busqueda || '%' ELSE TRUE END
      AND CASE WHEN p_estatus IS NOT NULL THEN cum.opc_estatus = p_estatus ELSE TRUE END
      LIMIT p_limit
      OFFSET p_offset
    ) consulta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerusuariosmonitores(
	p_busqueda TEXT,
	p_limit INT,
	p_offset INT,
	p_estatus BOOLEAN
) IS 'Esta función sirve para obtener todos los usuarios activos registrados para los monitores de conciliación';
