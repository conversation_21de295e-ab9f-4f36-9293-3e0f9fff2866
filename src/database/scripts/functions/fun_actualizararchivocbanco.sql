CREATE OR REPLACE FUNCTION fun_actualizararchivocbanco(cFecha DATE) RETURNS INT AS $$
DECLARE
	afectados 			INTEGER;
  	iduCbancoArchivo 	INTEGER;
  	nomArchivo          TEXT;
	squery              TEXT;
BEGIN

CREATE TEMPORARY TABLE tmp_cbanco_lineas(des_linea TEXT) ON COMMIT DROP;
nomArchivo = 'CBANCO.F' || to_char(cFecha,'YYYYMMDD')::TEXT || '.txt';
squery = 'COPY tmp_cbanco_lineas FROM ''/tmp/'|| nomArchivo ||'''';
EXECUTE squery;

SELECT idu_cbanco_archivo INTO iduCbancoArchivo
FROM ctl_cbanco_archivos
WHERE fec_archivo = (SELECT TO_DATE(substring(des_linea, 16, 8), 'DDMMYYYY') FROM tmp_cbanco_lineas WHERE substring(des_linea,1,1) = '0');

UPDATE ctl_cbanco_archivos 
SET 
	num_movimientos = substring(des_linea, 26, 6)::INT,
	imp_archivo = substring(des_linea, 32, 15)::INT
FROM tmp_cbanco_lineas tmp
WHERE substring(des_linea,1,1) = '0'
	AND idu_cbanco_archivo = iduCbancoArchivo;


INSERT INTO ctl_cbanco_archivo_detalles (
	idu_cbanco_archivo,
	clv_rpu,
	imp_detalle,
	fec_vencimiento,
	fec_movimiento
)
SELECT
	iduCbancoArchivo,
	substring(des_linea, 27, 12),
	substring(des_linea, 75, 10)::INT,
	TO_DATE(substring(des_linea, 47, 8), 'DDMMYYYY'),
	TO_DATE(substring(des_linea, 55, 8), 'DDMMYYYY')
FROM tmp_cbanco_lineas tmp
LEFT OUTER JOIN ctl_cbanco_archivo_detalles cad ON cad.clv_rpu = substring(des_linea, 27, 12)
WHERE cad.clv_rpu IS NULL
AND substring(des_linea,1,1) NOT IN ('0','9');

GET DIAGNOSTICS afectados = ROW_COUNT;
RETURN afectados;
END $$ LANGUAGE plpgsql VOLATILE