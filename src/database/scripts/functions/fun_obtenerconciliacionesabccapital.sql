CREATE OR REPLACE FUNCTION fun_obtenerconciliacionesabccapital(
	p_fecha DATE DEFAULT CURRENT_TIMESTAMP,
	p_estatus INT DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/10/2023
-- Descripción General: Esta función sirve para obtener las estadísticas de
-- la conciliación ABC Capital.
-- ========================================================================

	RETURNS TABLE (
		idu_bitacora_conciliacion INT,
		fecha TEXT,
		archivo_origen TEXT,
		descripcion TEXT,
		registros_origen BIGINT,
		incidencias BIGINT,
		estatus TEXT
	) AS
$BODY$
DECLARE
  descripcion CONSTANT TEXT := 'Contiene los registros de Pago proporcionados por ABC Capital';
BEGIN
	RETURN QUERY
	SELECT
		hbc.idu_bitacora_conciliacion AS idu_bitacora_conciliacion,
		hbc.fec_conciliacion::DATE::TEXT AS fecha,
		TRIM(mcac.des_archivo) as archivo_origen,
		descripcion,
		COUNT(mcac.idu_conciliacion_abc_capital) AS registros_origen,
		SUM(CASE WHEN mcac.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END) AS incidencias,
		(CASE WHEN(SUM(CASE WHEN mcac.idu_estatus_conciliacion = 2 THEN 0 ELSE 1 END) > 0) THEN 'Diferencias' ELSE 'Exitoso' END) AS estatus
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_abc_capital mcac ON hbc.idu_bitacora_conciliacion = mcac.idu_bitacora_conciliacion
	WHERE hbc.fec_conciliacion = p_fecha
	GROUP BY hbc.idu_bitacora_conciliacion, mcac.des_archivo
	HAVING CASE WHEN p_estatus IS NOT NULL THEN (CASE WHEN(SUM(CASE WHEN mcac.idu_estatus_conciliacion = 2 THEN 0 ELSE 1 END) > 0) THEN 0 ELSE 1 END) = p_estatus ELSE TRUE END;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerconciliacionesabccapital(
	p_fecha DATE,
	p_estatus INT
) IS 'Esta función sirve para obtener las estadísticas de la conciliación ABC Capital.
p_fecha: Fecha de la conciliación.
p_estatus: Estatus de la conciliación.';
