CREATE OR REPLACE FUNCTION fun_obtenerperfilmonitores(p_idu_perfil INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función sirve para obtener un perfil por
-- su identificador.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
    modulos JSON := (
        SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT('idu_modulo', idu_modulo)), '[]')
        FROM cat_permiso_monitores
        WHERE idu_perfil = p_idu_perfil AND opc_activo = TRUE
    );
    permisos_ejecucion JSON := (
        SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT('idu_conciliacion', idu_conciliacion)), '[]')
        FROM cat_perfil_permiso_ejecucion_manual_monitores
        WHERE idu_perfil = p_idu_perfil AND opc_activo
    );
BEGIN
	RETURN (
        SELECT
            JSON_BUILD_OBJECT(
                'idu_perfil', cpm.idu_perfil,
                'nom_perfil', cpm.nom_perfil,
                'des_perfil', cpm.des_perfil,
                'opc_estatus', cpm.opc_estatus,
                'modulos', modulos,
                'permisos_ejecucion', permisos_ejecucion
            ) as resultado
        FROM
        cat_perfil_monitores cpm
        WHERE
        cpm.opc_activo = TRUE
        AND cpm.idu_perfil > 0
        AND cpm.idu_perfil = p_idu_perfil
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerperfilmonitores(p_idu_perfil INT) IS 'Esta función sirve para obtener un perfil por su identificador,
p_idu_perfil= Identificador del perfil a obtener';
