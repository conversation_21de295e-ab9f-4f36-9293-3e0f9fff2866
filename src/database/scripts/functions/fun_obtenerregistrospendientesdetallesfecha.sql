CREATE OR REPLACE FUNCTION fun_obtenerregistrospendientesdetallesfecha(pagina INT, numRegistros INT, fechaDetalle DATE)
RETURNS TABLE (
   fecha       	DATE,
   rpu					CHAR(1),
   caja 				SMALLINT,
   importe     	INTEGER,
	 fol_sucursal BIGINT,
   clave 				CHAR(1),
   tienda				SMALLINT,
	 count 			 	BIGINT
)
LANGUAGE plpgsql AS
$$
BEGIN
   RETURN QUERY
   SELECT a.fecha, a.rpu, a.caja, a.importe, a.fol_sucursal, a.clave, a.tienda, COUNT(1) OVER() AS count
   FROM tiecarmovhistorial a
   WHERE a.fecha = fechaDetalle
      AND a.clave = 'W'
      AND a.fol_sucursal != 0
   GROUP BY a.fecha, a.rpu, a.caja, a.importe, a.fol_sucursal, a.clave, a.tienda
	 ORDER BY a.fecha, a.clave
	 LIMIT numRegistros OFFSET (pagina - 1) * numRegistros;
END
$$;