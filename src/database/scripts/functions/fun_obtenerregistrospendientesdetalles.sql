DROP FUNCTION IF EXISTS fun_obtenerregistrospendientes(INT, INT, DATE, TEXT);
CREATE OR REPLACE FUNCTION fun_obtenerregistrospendientesdetalles(pagina INT, numRegistros INT, fechaFiltro DATE, busqueda TEXT)
RETURNS TABLE (
   fecha DATE,
   fechaVencimiento DATE,
   rpu CHAR(1),
   caja SMALLINT,
   importe INTEGER,
   fol_sucursal BIGINT,
   clave CHAR(1),
   tienda SMALLINT,
   count BIGINT
) LANGUAGE plpgsql AS
$$
BEGIN
RETURN QUERY
SELECT
   a.fecha,
   a.fechavencimiento,
   a.rpu,
   a.caja,
   a.importe,
   a.fol_sucursal,
   a.clave,
   a.tienda,
   COUNT(1) OVER() AS count
FROM
   tiecarmovhistorial a
   LEFT JOIN ctl_cbanco_archivo_detalles cad ON (a.rpu = cad.clv_rpu)
WHERE
   (
      a.fecha <= COALESCE(fechaFiltro, (NOW() - INTERVAL '7 days')) :: DATE
      AND a.fecha >= COALESCE(fechaFiltro, (NOW() - INTERVAL '40 days')) :: DATE
   )
   AND a.clave IN ('W')
   AND a.fol_sucursal != 0
   AND cad.clv_rpu IS NULL
   AND (
      CAST(a.fol_sucursal AS TEXT) LIKE '%' || busqueda || '%'
      OR a.rpu LIKE '%' || COALESCE(busqueda, a.rpu) || '%'
   )
ORDER BY
   a.fecha,
   a.clave
LIMIT
   numRegistros OFFSET (pagina - 1) * numRegistros;

END
$$;