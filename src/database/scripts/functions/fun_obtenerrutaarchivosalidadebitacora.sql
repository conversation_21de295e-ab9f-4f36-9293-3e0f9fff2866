CREATE OR REPLACE FUNCTION fun_obtenerrutaarchivosalidadebitacora(p_idu_bitacora_conciliacion INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 05/10/2023
-- Descripción General: Esta función sirve para obtener el archivo de
-- salida de una bitacora.
-- ========================================================================

  RETURNS TEXT AS
$BODY$
BEGIN
	RETURN (
		SELECT des_ruta_archivo_salida
		FROM his_bitacora_conciliaciones
		WHERE idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerrutaarchivosalidadebitacora(p_idu_bitacora_conciliacion INT) IS 'Esta función sirve para obtener el archivo de salida de una bitacora.
p_idu_bitacora_conciliacion= Identificador de la bitacora de conciliación a obtener';
