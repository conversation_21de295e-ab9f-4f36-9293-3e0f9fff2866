
CREATE OR REPLACE FUNCTION fun_guardararchivocbanco (txtarchivos TEXT) RETURNS INT AS $$
DECLARE
  afectados integer;
BEGIN
DROP TABLE IF EXISTS temp_table;
CREATE TEMP TABLE temp_table AS
WITH tbl_archivos (x) AS (
	SELECT txtarchivos :: XML AS xmlarchivos
)
SELECT 
		(XPATH('./archivo/nom_archivo/text()', x))[1]::TEXT AS Nombre,
		(XPATH('./archivo/fec_archivo/text()', x))[1]::TEXT AS Fecha,
		(XPATH('./archivo/num_movimientos/text()', x))[1]::TEXT AS Movimientos,
		(XPATH('./archivo/imp_archivo/text()', x))[1]::TEXT AS Importe,
		UNNEST(XPATH('./archivo/detalles/detalle/clv_rpu/text()', x))::TEXT AS Rpu,
		UNNEST(XPATH('./archivo/detalles/detalle/fec_vencimiento/text()', x))::TEXT AS FechaVencimiento,
		UNNEST(XPATH('./archivo/detalles/detalle/fec_movimiento/text()', x))::TEXT AS FechaMovimiento,
		UNNEST(XPATH('./archivo/detalles/detalle/imp_detalle/text()', x))::TEXT AS ImporteDetalle
FROM tbl_archivos;
	
INSERT INTO ctl_cbanco_archivos (
nom_archivo,
fec_archivo,
num_movimientos,
imp_archivo
)
SELECT DISTINCT
Nombre,
Fecha::DATE,
Movimientos::INTEGER,
Importe::INTEGER
FROM temp_table;

INSERT INTO ctl_cbanco_archivo_detalles (
idu_cbanco_archivo,
clv_rpu,
imp_detalle,
fec_vencimiento,
fec_movimiento
)
SELECT
currval('ctl_cbanco_archivos_idu_cbanco_archivo_seq'),
tmp.Rpu,
tmp.ImporteDetalle::INTEGER,
tmp.FechaVencimiento::DATE,
tmp.FechaMovimiento::DATE
FROM temp_table tmp;

GET DIAGNOSTICS afectados = ROW_COUNT;
RETURN afectados;
END $$ LANGUAGE 'plpgsql';
