DROP FUNCTION IF EXISTS fun_obtenerorigenporclave(TEXT);
CREATE OR REPLACE FUNCTION fun_obtenerorigenporclave(clave TEXT)
RETURNS TABLE (
	clv_origen TEXT,
	des_origen TEXT,
	des_servidor TEXT,
	des_ruta TEXT,
	des_usuario TEXT,
	des_contrasena TEXT,
	nom_archivo TEXT,
	des_extension VARCHAR
)
LANGUAGE plpgsql AS
$func$
BEGIN
	RETURN QUERY
   	SELECT 
		co.clv_origen,
		co.des_origen,
		co.des_servidor,
		co.des_ruta,
		co.des_usuario,
		co.des_contrasena,
		co.nom_archivo,
		co.des_extension
   	FROM cat_conciliacion_origenes co
	WHERE co.clv_origen = clave;
	 
END
$func$;