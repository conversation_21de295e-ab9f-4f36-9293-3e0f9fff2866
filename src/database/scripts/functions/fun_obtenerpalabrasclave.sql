CREATE OR REPLACE FUNCTION public.fun_obtenerpalabrasclave(
)

-- =============================================
-- Autor:               Gaman Solutions
-- Fecha:               02/01/2024
-- Descripción General: Esta función sirve para obtener las palabras clave de un correo de conciliación
-- =============================================

RETURNS JSON AS
$BODY$
BEGIN
    RETURN (
		SELECT
            COALESCE(JSON_AGG(
                JSON_BUILD_OBJECT(
                'idu_conciliacion_reemplazo_cadena', p.idu_conciliacion_reemplazo_cadena,
                'des_remplazo', p.des_reemplazo,
                'des_cadena_reemplazar', p.des_cadena_reemplazar,
                'des_cadena_reemplazo', p.des_cadena_reemplazo
            )), '[]')
        FROM (
            SELECT
                p.idu_conciliacion_reemplazo_cadena,
                p.des_reemplazo,
                p.des_cadena_reemplazar,
                p.des_cadena_reemplazo
            FROM cat_conciliacion_reemplazo_cadenas p
            ORDER BY p.des_cadena_reemplazar
        ) as p
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.fun_obtenerpalabrasclave(
) IS 'Esta función sirve para obtener las palabras clave de un correo de conciliación';
