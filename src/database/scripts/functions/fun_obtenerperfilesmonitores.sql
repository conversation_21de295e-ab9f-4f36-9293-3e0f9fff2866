CREATE OR REPLACE FUNCTION fun_obtenerperfilesmonitores(
    p_busqueda TEXT DEFAULT '',
    p_limit INT DEFAULT 10,
    p_offset INT DEFAULT 0,
    p_estatus BOOLEAN DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función sirve para obtener todos los perfiles
-- activos registrados para los monitores de conciliación páginados.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
		SELECT
            JSON_BUILD_OBJECT (
                'total',
                (
                    SELECT COUNT ( * )
                    FROM cat_perfil_monitores
                    WHERE opc_activo
                        AND CASE WHEN p_busqueda != '' THEN nom_perfil ILIKE '%' || p_busqueda || '%' ELSE TRUE END
                        AND CASE WHEN p_estatus IS NOT NULL THEN opc_estatus = p_estatus ELSE TRUE END
                ),
                'registros',
                COALESCE(JSON_AGG (
                    JSON_BUILD_OBJECT (
                        'idu_perfil',
                        consulta.idu_perfil,
                        'nom_perfil',
                        consulta.nom_perfil,
                        'des_perfil',
                        consulta.des_perfil,
                        'opc_estatus',
                        consulta.opc_estatus,
                        'modulos',
                        consulta.modulos,
                        'modulos_padre',
                        consulta.modulos_padre
                    )
                ) , '[]')
            )
        FROM (
        SELECT
            cpm.idu_perfil,
            cpm.nom_perfil,
            cpm.des_perfil,
            cpm.opc_estatus,
            (SELECT COALESCE (JSON_AGG (JSON_BUILD_OBJECT ('idu_modulo',A.idu_modulo,'nombre',b.nom_modulo,'idu_modulo_padre',b.idu_modulo_padre)),'[]') FROM cat_permiso_monitores A INNER JOIN cat_modulo_monitores b ON A.idu_modulo=b.idu_modulo WHERE A.idu_perfil=cpm.idu_perfil AND A.opc_activo=TRUE) AS modulos,
            (SELECT COALESCE (JSON_AGG (JSON_BUILD_OBJECT ('idu_modulo',idu_modulo,'nombre',nombre)),'[]') FROM (SELECT C.idu_modulo,C.nom_modulo AS nombre FROM cat_permiso_monitores A INNER JOIN cat_modulo_monitores B ON A.idu_modulo=b.idu_modulo INNER JOIN cat_modulo_monitores C ON B.idu_modulo_padre=C.idu_modulo WHERE A.idu_perfil=cpm.idu_perfil AND A.opc_activo=TRUE GROUP BY C.idu_modulo) T) AS modulos_padre
        FROM
            cat_perfil_monitores cpm
        WHERE
            cpm.opc_activo = TRUE
            AND cpm.idu_perfil > 0
            AND CASE WHEN p_busqueda != '' THEN cpm.nom_perfil ILIKE '%' || p_busqueda || '%' ELSE TRUE END
            AND CASE WHEN p_estatus IS NOT NULL THEN cpm.opc_estatus = p_estatus ELSE TRUE END
        LIMIT p_limit
            OFFSET p_offset
        ) consulta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerperfilesmonitores(
	p_busqueda TEXT,
	p_limit INT,
	p_offset INT,
	p_estatus BOOLEAN
) IS 'Esta función sirve para obtener todos los perfiles activos registrados para los monitores de conciliación';
