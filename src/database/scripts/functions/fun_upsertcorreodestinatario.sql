CREATE OR REPLACE FUNCTION fun_upsertcorreodestinatario(
	p_idu_correo_destinatario INT DEFAULT NULL,
	p_des_destinatario TEXT DEFAULT '',
    p_nom_destinatario TEXT DEFAULT '',
    p_idu_conciliacion INT DEFAULT NULL,
    p_opc_estatus BOOLEAN DEFAULT TRUE,
    p_tipo TEXT DEFAULT ''
)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 18/12/2023
-- Descripción General: Esta función inserta o actualiza, si mandas el
-- identificador, un registro en la cat_conciliacion_correo_destinatario.
-- ========================================================================
RETURNS JSON AS
$BODY$
DECLARE
  esActualizacion BOOLEAN := p_idu_correo_destinatario IS NOT NULL;
  esDuplicado BOOLEAN := FALSE;
  fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
  registro_insertado INT := 0;
  plantillaAlerta BIT := CASE WHEN p_tipo = 'Soporte' THEN '1' ELSE '0' END;
  p_idu_correo INT := 0;
BEGIN
    --Obtener la plantilla de correo
    p_idu_correo := (SELECT
        idu_correo
    FROM cat_conciliacion_correos
    WHERE idu_conciliacion = p_idu_conciliacion
    AND opc_alerta = plantillaAlerta);

    esDuplicado := EXISTS(
        SELECT 1
        FROM cat_conciliacion_correo_destinatarios
        WHERE des_destinatario = p_des_destinatario
        AND  idu_correo = p_idu_correo
        AND (idu_correo_destinatario != p_idu_correo_destinatario OR p_idu_correo_destinatario IS NULL)
    );

    IF esDuplicado THEN
        RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'El correo ya se encuentra registrado, favor de verificarlo');
    END IF;


	IF esActualizacion THEN -- Actualizar
        UPDATE cat_conciliacion_correo_destinatarios
        SET des_destinatario = p_des_destinatario,
            fec_actualizacion = fecha,
            idu_correo = p_idu_correo,
            nom_destinatario = p_nom_destinatario,
            opc_estatus =  CASE WHEN p_opc_estatus THEN CAST('1' AS BIT) ELSE CAST('0' AS BIT) END
        WHERE idu_correo_destinatario = p_idu_correo_destinatario;
	ELSE -- Insertar
	    INSERT INTO cat_conciliacion_correo_destinatarios (
            idu_correo,
		    nom_destinatario,
		    des_destinatario,
            opc_estatus,
		    fec_registro
	    ) VALUES (
            p_idu_correo,
            p_nom_destinatario,
            p_des_destinatario,
            CASE WHEN p_opc_estatus THEN CAST('1' AS BIT) ELSE CAST('0' AS BIT) END,
            fecha
	    )
	    RETURNING idu_correo_destinatario INTO p_idu_correo_destinatario; -- Recupera el nuevo idu_usuario generado
	END IF;

	-- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente' ELSE 'Registro insertado correctamente' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsertcorreodestinatario(
	p_idu_correo_destinatario INT,
	p_des_destinatario TEXT,
    p_nom_destinatario TEXT,
    p_idu_conciliacion INT,
    p_opc_estatus BOOLEAN,
    p_tipo TEXT
) IS 'Esta función inserta o actualiza, si mandas el identificador, un registro en la cat_conciliaciones_correo_destinatarios
p_idu_correo_destinatario=Idu del destinatario,
p_des_destinatario=Correo del destinatario,
p_nom_destinatario=Nombre del destinatario,
p_idu_conciliacion=Idu conciliacion,
p_opc_estatus=Estatus del destinatario,
p_tipo=Tipo del correo';
