CREATE OR REPLACE FUNCTION fun_obtenerarchivofecha(fecha DATE)
RETURNS TABLE (
   idu_cbanco_archivo	INTEGER,
   nom_archivo     		CHAR(20),
   fec_archivo     		DATE,
   num_movimientos		INTEGER,
   imp_archivo     		INTEGER,
   count       		 		BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
   RETURN QUERY
   SELECT  arc.idu_cbanco_archivo, arc.nom_archivo, arc.fec_archivo, arc.num_movimientos, arc.imp_archivo, COUNT(*) OVER() AS count
   FROM ctl_cbanco_archivos arc
	 WHERE arc.fec_archivo = fecha;
	 
END
$func$;
