CREATE OR REPLACE FUNCTION fun_eliminarperfilmonitores (p_idu_perfil INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función sirve para eliminar lógicamente
-- un perfil del monitor de conciliaciones.
-- ========================================================================

	RETURNS BOOLEAN AS

$BODY$
DECLARE
  registros_eliminados INT := 0;
  fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
BEGIN
  UPDATE cat_perfil_monitores
  SET opc_activo = FALSE,
    fec_actualizacion = fecha
  WHERE idu_perfil = p_idu_perfil;
	
  -- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registros_eliminados = ROW_COUNT;

  -- Devolver el resultado (TRUE si se insertó con éxito, FALSE si hubo un error)
	RETURN registros_eliminados::BOOLEAN;

  EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al eliminar registro: %', SQLERRM;
	RETURN registros_eliminados::BOOLEAN;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_eliminarperfilmonitores(p_idu_perfil INT) IS 'Esta función sirve para eliminar lógicamente un perfil del monitor de conciliaciones,
p_idu_perfil= Identificador del perfil a obtener';
