CREATE OR REPLACE FUNCTION fun_obtenerestadisticasgeneralesconciliaciones(
    p_idu_conciliacion INT,
    p_fecha DATE DEFAULT CURRENT_TIMESTAMP
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 17/10/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- generales de una conciliación.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
	idu_corresponsales INT := 1;
	idu_afore INT := 12;
	idu_abc_capital INT := 13;
BEGIN
  CASE
    WHEN p_idu_conciliacion = idu_corresponsales THEN
      RETURN fun_obtenerestadisticageneralcorresponsales(p_fecha);
    WHEN p_idu_conciliacion = idu_afore THEN
      RETURN fun_obtenerestadisticageneralafore(p_fecha);
    WHEN p_idu_conciliacion = idu_abc_capital THEN
      RETURN fun_obtenerestadisticageneralabccapital(p_fecha);
    ELSE
      RETURN JSON_BUILD_OBJECT('error', 'El nombre de la conciliación no es válido.', 'publico', true);
	END CASE;
	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener las estadísticas generales de la conciliación: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadisticasgeneralesconciliaciones(
	p_idu_conciliacion INT,
	p_fecha DATE
) IS 'Esta función sirve para obtener las estadísticas generales de una conciliación.
p_idu_conciliacion= Identificador de la conciliación.
p_fecha= Fecha de la conciliación.';
