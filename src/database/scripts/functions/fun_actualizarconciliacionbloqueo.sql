DROP FUNCTION IF EXISTS fun_actualizarconciliacionbloqueo(INT, INT, TEXT, DATE, BIT, INT);
CREATE OR REPLACE FUNCTION fun_actualizarconciliacionbloqueo(
  iduBloqueo INT,
  iduConciliacion INT,
  mensaje TEXT,
  fecBloqueo DATE,
  opcEstatus BIT,
  estatusConciliacion INT
) RETURNS INT AS $$
DECLARE
  afectados integer;
BEGIN
UPDATE
  cat_conciliacion_bloqueos
SET
  idu_conciliacion = iduConciliacion,
  des_mensaje_bitacora = mensaje,
  fec_bloqueo = fecBloqueo,
  opc_estatus = opcEstatus,
  idu_estatus_conciliacion = estatusConciliacion
WHERE
  idu_conciliacion_bloqueo = iduBloqueo;
GET DIAGNOSTICS afectados = ROW_COUNT;
RETURN afectados;
END $$ LANGUAGE 'plpgsql';