CREATE OR REPLACE FUNCTION fun_obtenerestadoconciliacion(p_idu_bitacora_conciliacion INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 14/11/2023
-- Descripción General: Esta función sirve para obtener el estado actual de
-- la conciliación.
-- ========================================================================

	RETURNS TABLE (
        idu_conciliacion_etapa INT,
		orden INT,
		etapa TEXT,
		icono TEXT,
		estatus TEXT
	) AS
$BODY$
BEGIN
  RETURN QUERY
	SELECT
        cce.idu_conciliacion_etapa,
        cce.num_orden AS orden,
        cce.des_etapa AS etapa,
        cce.nom_icono AS icono,
        MIN(cec.des_descripcion) AS estatus
    FROM his_bitacora_conciliaciones hbc
        INNER JOIN cat_conciliaciones cc
            ON hbc.idu_conciliacion = cc.idu_conciliacion
        INNER JOIN cat_conciliacion_etapas cce
            ON cce.idu_conciliacion = cc.idu_conciliacion
        LEFT JOIN his_bitacora_conciliacion_detalles hbcd
            ON cce.idu_conciliacion_etapa = hbcd.idu_conciliacion_etapa
            AND hbc.idu_bitacora_conciliacion = hbcd.idu_bitacora_conciliacion
        LEFT JOIN cat_estatus_conciliaciones cec
            ON hbcd.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
    WHERE hbc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
    GROUP BY cce.idu_conciliacion_etapa
    ORDER BY cce.num_orden ASC;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadoconciliacion(p_idu_bitacora_conciliacion INT) IS 'Esta función sirve para obtener el estado actual de la conciliación.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación';
