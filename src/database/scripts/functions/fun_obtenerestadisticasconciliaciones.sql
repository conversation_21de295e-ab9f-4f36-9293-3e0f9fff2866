CREATE OR REPLACE FUNCTION fun_obtenerestadisticasconciliaciones(
    p_idu_conciliacion INT,
    p_fecha DATE DEFAULT CURRENT_TIMESTAMP,
    p_estatus INT DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/10/2023
-- Descripción General: Esta función sirve para obtener las estadísticas de
-- un tipo de conciliación.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
	idu_corresponsales INT := 1;
	idu_afore INT := 12;
	idu_abc_capital INT := 13;
BEGIN
  CASE
    WHEN p_idu_conciliacion = idu_corresponsales THEN
      RETURN
        JSON_AGG(JSON_BUILD_OBJECT(
            'idu_bitacora_conciliacion', idu_bitacora_conciliacion,
			'fecha', fecha,
			'descripcion', descripcion,
            'archivo_origen', archivo_origen,
            'registros_origen', registros_origen,
            'incidencias', incidencias,
            'estatus', estatus
        ))
        FROM fun_obtenerconciliacioncorresponsales(p_fecha, p_estatus);
    WHEN p_idu_conciliacion = idu_afore THEN
      RETURN
         JSON_AGG(JSON_BUILD_OBJECT(
			'idu_bitacora_conciliacion', idu_bitacora_conciliacion,
			'fecha', fecha,
            'archivo_origen', archivo_origen,
			'descripcion', descripcion,
            'registros_origen', registros_origen,
            'incidencias', incidencias,
            'estatus', estatus
        ))
        FROM fun_obtenerconciliacionafore(p_fecha, p_estatus);
    WHEN p_idu_conciliacion = idu_abc_capital THEN
      RETURN
         JSON_AGG(JSON_BUILD_OBJECT(
			'idu_bitacora_conciliacion', idu_bitacora_conciliacion,
			'fecha', fecha,
            'archivo_origen', archivo_origen,
			'descripcion', descripcion,
            'registros_origen', registros_origen,
            'incidencias', incidencias,
            'estatus', estatus
        ))
        FROM fun_obtenerconciliacionesabccapital(p_fecha, p_estatus);
    ELSE
      RETURN JSON_BUILD_OBJECT('error', 'El nombre de la conciliación no es válido.', 'publico', true);
	END CASE;
	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener las estadísticas de conciliación: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerestadisticasconciliaciones(
	p_idu_conciliacion INT,
	p_fecha DATE,
	p_estatus INT
) IS 'Esta función sirve para obtener las estadísticas de un tipo de conciliación.
p_idu_conciliacion= Identificador de la conciliación.
p_fecha= Fecha de la conciliación.
p_estatus= Estatus de la conciliación.';
