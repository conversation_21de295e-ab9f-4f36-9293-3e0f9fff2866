CREATE OR REPLACE FUNCTION fun_obtenerbitacorasconciliaciones(
		p_idu_conciliacion INT DEFAULT NULL,
		p_fecha DATE DEFAULT CURRENT_DATE,
		p_idu_estatus_conciliacion INT DEFAULT NULL,
		p_busqueda TEXT DEFAULT '',
		p_tipo_servicio TEXT DEFAULT ''
	)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/11/2023
-- Descripción General: Esta función sirve para obtener las bitácoras de
-- conciliaciones.
-- ========================================================================

	RETURNS TABLE (
		idu_conciliacion INT,
		nombre_conciliacion TEXT,
		idu_bitacora_conciliacion INT,
		descripcion TEXT,
		completados INT,
		pendientes INT,
		tipo_ejecucion TEXT,
		usuario TEXT,
		estatus TEXT,
		fecha_inicio TIMESTAMP,
		fecha_fin TIMESTAMP,
		opc_correo_enviado BOOLEAN
	) AS
$BODY$
DECLARE
	idus_pago_servicios INT[] := ARRAY[
		2,  -- Pago de servicios - Patrimonio
		3,  -- Pago de servicios - Telmex
		4,  -- Pago de servicios - Japac
		5,  -- Pago de servicios - CF
		6,  -- Pago de servicios - ABC Capital
		7,  -- Pago de servicios - Movistar
		8,  -- Pago de servicios - Credito y Casa
		9,  -- Pago de servicios - Blackhawk
        14, -- Liquidacion pago de servicios - CFE
        15, -- Reporte Liquidacion Japac
        16, -- Reporte de liquidacion - Patrimonio semanal
        17, -- Pago de servicios semanales - Telmex
        18, -- Reporte Liquidacion - OPERAX
        19  -- Reporte Liquidacion - Movistar
	];
	idus_homologados INT[] := ARRAY[
		10, -- Pago de servicios homologados - CFE
		11  -- WS Conciliaciones Proceso Interno
	];
	idus_conciliaciones INT[] := ARRAY[
		1,  -- Conciliacion Corresponsales
        12, -- Conciliacion Afore
        13  -- Conciliacion ABC Capital
    ];
    idu_tipo_paso_envio_correo INT := 4;
    idu_estatus_exitoso INT = 2;
BEGIN
  RETURN QUERY
	SELECT
		hbc.idu_conciliacion,
		cc.nom_conciliacion as nombre_conciliacion,
		hbc.idu_bitacora_conciliacion,
		hbc.des_descripcion as descripcion,
		hbc.num_completado as completados,
		hbc.num_pendiente as pendientes,
		CASE WHEN hbc.opc_ejecucion_manual = B'0' THEN 'Automatica' ELSE 'Manual' END as tipo_ejecucion,
		hbc.idu_usuario_registro as usuario,
		cec.des_descripcion as estatus,
		hbc.fec_registro as fecha_inicio,
		hbc.fec_actualizacion as fecha_fin,
        NOT EXISTS(SELECT idu_estatus_conciliacion
            FROM his_bitacora_conciliacion_detalles hbcd_temp
            INNER JOIN cat_conciliacion_pasos cpp
            ON hbcd_temp.idu_conciliacion_paso = cpp.idu_conciliacion_paso
            AND cpp.idu_conciliacion_tipo  = idu_tipo_paso_envio_correo
            WHERE hbcd_temp.idu_bitacora_conciliacion = hbc.idu_bitacora_conciliacion
            AND hbcd_temp.idu_estatus_conciliacion != idu_estatus_exitoso) AS opc_correo_enviado
	FROM
		his_bitacora_conciliaciones hbc
			INNER JOIN cat_estatus_conciliaciones cec ON hbc.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
			INNER JOIN cat_conciliaciones cc ON hbc.idu_conciliacion = cc.idu_conciliacion
	WHERE
		hbc.fec_conciliacion::DATE = p_fecha
		AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN hbc.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
		AND CASE WHEN p_idu_conciliacion IS NOT NULL THEN cc.idu_conciliacion = p_idu_conciliacion ELSE TRUE END
		AND CASE WHEN p_tipo_servicio = 'conciliacion' THEN cc.idu_conciliacion = ANY(idus_conciliaciones) ELSE TRUE END
		AND CASE WHEN p_tipo_servicio = 'pago_servicio' THEN cc.idu_conciliacion = ANY(idus_pago_servicios) ELSE TRUE END
		AND CASE WHEN p_tipo_servicio = 'homologados' THEN cc.idu_conciliacion = ANY(idus_homologados) ELSE TRUE END
		AND CASE WHEN (p_idu_conciliacion IS NULL AND p_tipo_servicio = '') THEN FALSE ELSE TRUE END
		AND CASE WHEN p_busqueda != '' THEN hbc.des_descripcion ILIKE '%' || p_busqueda || '%' ELSE TRUE END;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerbitacorasconciliaciones(
  p_idu_conciliacion INT,
  p_fecha DATE,
  p_idu_estatus_conciliacion INT,
  p_busqueda TEXT,
  p_tipo_servicio TEXT
) IS 'Esta función sirve para obtener las bitácoras de conciliaciones.
p_idu_conciliacion= Identificador de la conciliación a obtener.
p_fecha= Fecha de la conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la conciliación a obtener.
p_busqueda= Texto a buscar en la descripción de la conciliación.
p_tipo_servicio= Tipo de servicio a obtener.';
