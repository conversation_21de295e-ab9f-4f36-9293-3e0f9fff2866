CREATE OR REPLACE FUNCTION fun_upsertaccioncorreccion(
  p_idu_bitacora_conciliacion INT,
  p_idu_estatus_conciliacion INT,
  p_persona_inspeccion TEXT DEFAULT '',
  p_observacion TEXT DEFAULT '',
  p_idu_bitacora_accion_correccion INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 06/11/2023
-- Descripción General: Esta función inserta o actualiza una acción o
-- corrección en la his_bitacora_accion_correcciones
-- ========================================================================

  RETURNS JSON AS

$BODY$
DECLARE
    esActualizacion BOOLEAN := p_idu_bitacora_accion_correccion::BOOLEAN;
    fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
    registro_insertado INT := 0;
BEGIN
    IF esActualizacion THEN -- Actualizar
        UPDATE his_bitacora_accion_correcciones
        SET nom_persona_inspeccion = COALESCE(p_persona_inspeccion, nom_persona_inspeccion),
            des_observacion = COALESCE(p_observacion, des_observacion),
            idu_estatus_conciliacion = COALESCE(p_idu_estatus_conciliacion, idu_estatus_conciliacion),
            fec_actualizacion = fecha
        WHERE idu_bitacora_accion_correccion = p_idu_bitacora_accion_correccion;
    ELSE -- Insertar
        INSERT INTO his_bitacora_accion_correcciones (
            idu_bitacora_conciliacion,
            idu_estatus_conciliacion,
            nom_persona_inspeccion,
            des_observacion,
            fec_registro
        ) VALUES (
            p_idu_bitacora_conciliacion,
            p_idu_estatus_conciliacion,
            p_persona_inspeccion,
            p_observacion,
            fecha
        );
    END IF;

    -- Si la inserción se realiza correctamente, establecer el valor en TRUE
    GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente' ELSE 'Registro insertado correctamente' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
    END IF;

    EXCEPTION -- Capturar y manejar errores
        WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
    RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsertaccioncorreccion(
  p_idu_bitacora_conciliacion INT,
  p_idu_estatus_conciliacion INT,
  p_persona_inspeccion TEXT,
  p_observacion TEXT,
  p_idu_bitacora_accion_correccion INT
) IS 'Esta función inserta o actualiza una acción o corrección en la his_bitacora_accion_correcciones
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación
p_idu_estatus_conciliacion= Identificador del estatus de conciliación
p_persona_inspeccion= Nombre de la persona que inspeccionó
p_observacion= Observación
p_idu_bitacora_accion_correccion= Identificador de la acción o corrección';
