CREATE OR REPLACE FUNCTION fun_eliminardestinatariocorreo (p_idu_correo_destinatario INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función sirve para eliminar físicamente
-- un destinatario de correos de conciliación.
-- ========================================================================

	RETURNS BOOLEAN AS

$BODY$
DECLARE
  registros_eliminados INT := 0;
  fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
BEGIN
    DELETE FROM cat_conciliacion_correo_destinatarios
    WHERE idu_correo_destinatario = p_idu_correo_destinatario;

  -- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registros_eliminados = ROW_COUNT;

  -- Devolver el resultado (TRUE si se insertó con éxito, FALSE si hubo un error)
	RETURN registros_eliminados::BOOLEAN;

  EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al eliminar registro: %', SQLERRM;
	RETURN registros_eliminados::BOOLEAN;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_eliminardestinatariocorreo(p_idu_correo_destinatario INT) IS 'Esta función sirve para eliminar fisicamente un destinatario de los correos de conciliaciones,
p_idu_conciliacion= Identificador del destinatario a eliminar';
