CREATE OR REPLACE FUNCTION fun_obtenercontenidoarchivo(archivoid INT)
  RETURNS TABLE(linea text) AS $BODY$
DECLARE
	fechaArchivo char(8);
BEGIN
	SELECT TO_CHAR(ca.fec_archivo, 'YYYYMMDD') INTO fechaArchivo FROM ctl_cbanco_archivos ca WHERE ca.idu_cbanco_archivo = archivoId;
	
	--insertamos encabezado formateado
	CREATE TEMP TABLE tmp_lineas AS
	SELECT '000000732000000'||fechaArchivo||'00'|| lpad(cast(count(*) as char(6)),6,'0')||lpad(cast(sum(cad.imp_detalle) as char(15)),15,'0')||lpad('0',112,'0') AS linea
	FROM ctl_cbanco_archivo_detalles cad 
	INNER JOIN ctl_cbanco_archivos ca ON (ca.idu_cbanco_archivo = cad.idu_cbanco_archivo)
		WHERE cad.idu_cbanco_archivo = archivoId;
	
	--insertamos el cuerpo formateado
	INSERT INTO tmp_lineas
	SELECT '100000FA'||LPAD('0',18,'0')||clv_rpu||' '||lpad('0',7,'0')||lpad(substr(fec_movimiento::text,9,2),2,'0')||
	lpad(substr(fec_movimiento::text,6,2),2,'0')||lpad(substr(fec_movimiento::text,1,4),4,'0')||lpad(substr(fec_vencimiento::text,9,2),2,'0')||lpad(substr(fec_vencimiento::text,6,2),2,'0')||
	lpad(substr(fec_vencimiento::text,1,4),4,'0')||lpad(substr(fec_movimiento::text,9,2),2,'0')||lpad(substr(fec_movimiento::text,6,2),2,'0')||lpad(substr(fec_movimiento::text,1,4),4,'0')||
	lpad('0',4,'0')||lpad(cast(imp_detalle as char(10)),10,'0')||lpad('0',25,'0')||lpad(cast(imp_detalle as char(10)),10,'0')||lpad('0',39,'0')
	FROM ctl_cbanco_archivo_detalles d 
		WHERE d.idu_cbanco_archivo = archivoId;
	
	--insertamos el pie formateado
	INSERT INTO tmp_lineas
	select '900000'||lpad('0',18,'0')||lpad(cast(count(*) as char(6)),6,'0')||lpad(cast(sum(imp_detalle) as char(12)),12,'0')||lpad('0',36,'0')||
	lpad(cast(count(*) as char(6)),6,'0')||lpad(cast(sum(imp_detalle) as char(12)),12,'0')||lpad('0',62,'0')
	FROM ctl_cbanco_archivo_detalles d 
		WHERE d.idu_cbanco_archivo = archivoId;
	
	RETURN QUERY
	SELECT tmp.linea FROM tmp_lineas tmp;
	
	DROP TABLE tmp_lineas;
END
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100
  ROWS 1000