DROP FUNCTION IF EXISTS fun_obtenerbloqueosporconciliacion(INT, INT, INT);
CREATE OR REPLACE FUNCTION fun_obtenerbloqueosporconciliacion(iduConciliacion INT, pagina INT, numRegistros INT)
RETURNS TABLE (
   idu_conciliacion_bloqueo	INTEGER,
   fec_bloqueo     		      TIMESTAMP,
	fec_registro     		      TIMESTAMP,
	idu_usuario_registro       TEXT,
	nom_empleado               TEXT,
   idu_estatus_conciliacion	INTEGER,
	des_estatus                TEXT,
   count       		 		   BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
   RETURN QUERY
   SELECT 
      cb.idu_conciliacion_bloqueo,
      cb.fec_bloqueo,
      cb.fec_registro,
      cb.idu_usuario_registro,
      um.nom_empleado,
      cb.idu_estatus_conciliacion,
      ec.des_descripcion,
      COUNT(*) OVER() AS count
   FROM cat_conciliacion_bloqueos cb
   INNER JOIN cat_usuario_monitores um ON (cb.idu_usuario_registro = um.idu_usuario::TEXT)
   INNER JOIN cat_estatus_conciliaciones ec ON (cb.idu_estatus_conciliacion = ec.idu_estatus_conciliacion)
	 WHERE cb.idu_conciliacion = iduConciliacion
		AND cb.opc_estatus = b'1'
	 LIMIT numRegistros
	 OFFSET (pagina - 1) * numRegistros;
	 
END
$func$;