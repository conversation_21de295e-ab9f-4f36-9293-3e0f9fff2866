CREATE OR REPLACE FUNCTION public.fun_obtenerdestinatarios(
    p_iduconciliacion INT,
    p_limit INT,
    p_offset INT,
    p_busqueda TEXT
)

-- =============================================
-- Autor:               Gaman Solutions
-- Fecha:               29/12/2023
-- Descripción General: Esta función sirve para obtener los destinatarios de correo para una conciliación
-- =============================================

RETURNS JSON AS
$BODY$
BEGIN
    RETURN (
		SELECT
            JSON_BUILD_OBJECT (
                'total', (
                    SELECT COUNT (d.idu_correo_destinatario)
                    FROM
                        cat_conciliacion_correo_destinatarios d
                    INNER JOIN cat_conciliacion_correos c ON d.idu_correo = c.idu_correo
                    WHERE
                        c.idu_conciliacion = p_iduconciliacion
                        AND c.opc_estatus = '1'
                        AND (
                            p_busqueda = ''
                            OR  d.nom_destinatario ILIKE CONCAT('%', p_busqueda, '%')
                            OR d.des_destinatario ILIKE CONCAT('%', p_busqueda, '%')
                        )
                ),
                'registros',
                COALESCE(JSON_AGG(
                    JSON_BUILD_OBJECT(
                        'idu_correo_destinatario', idu_correo_destinatario,
                        'nom_destinatario', nom_destinatario,
                        'nom_conciliacion', nom_conciliacion,
                        'des_destinatario', des_destinatario,
                        'opc_estatus', CASE WHEN opc_estatus = '1' THEN TRUE ELSE FALSE END,
                        'tipo', CASE WHEN opc_alerta = '1' THEN 'Soporte' ELSE 'Usuario' END
                    )
                ) , '[]')
            )
        FROM (
            SELECT
                d.idu_correo_destinatario,
                d.nom_destinatario,
                co.nom_conciliacion,
                d.des_destinatario,
                d.opc_estatus,
                c.opc_alerta
            FROM cat_conciliacion_correo_destinatarios d
            INNER JOIN cat_conciliacion_correos c ON d.idu_correo = c.idu_correo
            INNER JOIN cat_conciliaciones co ON c.idu_conciliacion = co.idu_conciliacion
            WHERE
                c.idu_conciliacion = p_iduconciliacion
                AND c.opc_estatus = '1'
                AND (
                    p_busqueda = ''
                    OR d.nom_destinatario ILIKE CONCAT('%', p_busqueda, '%')
                    OR d.des_destinatario ILIKE CONCAT('%', p_busqueda, '%')
                )
            LIMIT p_limit
            OFFSET p_offset
        ) AS consulta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.fun_obtenerdestinatarios(
    p_iduconciliacion INT,
    p_limit INT,
    p_offset INT,
    p_busqueda TEXT
) IS 'Esta función sirve para obtener los destinatarios de correo para una conciliación';
