CREATE OR REPLACE FUNCTION public.fun_obtenercorreoconciliacion(
    p_idu_conciliacion INT,
    p_opc_alerta BIT DEFAULT '0'
)

-- =============================================
-- Autor:               Gaman Solutions
-- Fecha:               02/01/2024
-- Descripción General: Esta función sirve para obtener un correo de conciliación por el idu_conciliacion
-- =============================================

RETURNS JSON AS
$BODY$
BEGIN
    RETURN (
		SELECT
            JSON_BUILD_OBJECT(
                'idu_correo', c.idu_correo,
                'idu_conciliacion', c.idu_conciliacion,
                'des_asunto', c.des_asunto,
                'des_cuerpo', c.des_cuerpo,
                'opc_alerta', CASE WHEN c.opc_alerta = '1' THEN TRUE ELSE FALSE END,
                'opc_estatus', CASE WHEN c.opc_estatus = '1' THEN TRUE ELSE FALSE END
            )
        FROM cat_conciliacion_correos c
        WHERE c.idu_conciliacion = p_idu_conciliacion
        AND c.opc_alerta = p_opc_alerta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.fun_obtenercorreoconciliacion(
    p_idu_conciliacion INT,
    p_opc_alerta BIT
) IS 'Esta función sirve para obtener un correo de conciliación por el idu_conciliacion';
