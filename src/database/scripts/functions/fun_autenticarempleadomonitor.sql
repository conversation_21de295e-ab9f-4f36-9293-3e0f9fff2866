CREATE OR REPLACE FUNCTION fun_autenticarempleadomonitor (p_num_empleado INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 21/12/2023
-- Descripción General: Esta función sirve para autenticar que un empleado
-- tiene acceso al sistema de Monitor de Corresponsales.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
		SELECT
		  JSON_BUILD_OBJECT(
			'idu_usuario', cum.idu_usuario,
			'num_empleado', cum.num_empleado,
			'nombre', cum.nom_empleado,
			'puesto', cum.nom_puesto,
			'telefono', cum.num_telefono,
            'nom_perfil', cpm.nom_perfil,
			'modulos', (
				SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT('idu_modulo', cmm.idu_modulo, 'nombre', cmm.nom_modulo, 'ruta', cmm.des_ruta)), '[]')
				FROM cat_permiso_monitores cpm INNER JOIN cat_modulo_monitores cmm
					ON cpm.idu_modulo = cmm.idu_modulo
				WHERE cpm.idu_perfil = cum.idu_perfil AND cmm.opc_activo
			),
            'permisos_ejecucion', (
                SELECT COALESCE(JSON_AGG(JSON_BUILD_OBJECT('idu_conciliacion', cupemm.idu_conciliacion, 'nombre', cc.nom_conciliacion)), '[]')
                FROM cat_perfil_permiso_ejecucion_manual_monitores cupemm INNER JOIN cat_conciliaciones cc
                    ON cupemm.idu_conciliacion = cc.idu_conciliacion
                WHERE cupemm.idu_perfil = cum.idu_perfil AND cupemm.opc_activo
            )
		) as resultado
		FROM
		  cat_usuario_monitores cum INNER JOIN cat_perfil_monitores cpm ON cum.idu_perfil = cpm.idu_perfil
		WHERE cum.opc_activo AND cum.opc_estatus
		  AND cpm.opc_activo AND cpm.opc_estatus
		  AND cum.num_empleado = p_num_empleado
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_autenticarempleadomonitor(p_num_empleado INT) IS 'Esta función sirve para autenticar que un empleado tiene acceso al sistema de Monitor de Corresponsales.
p_num_empleado= Número de empleado.';
