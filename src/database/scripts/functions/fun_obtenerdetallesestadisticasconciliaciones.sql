CREATE OR REPLACE FUNCTION fun_obtenerdetallesestadisticasconciliaciones(
	p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_tipo_archivo CHAR(3),
	p_idu_estatus_conciliacion INT DEFAULT NULL,
	p_busqueda TEXT DEFAULT NULL,
	p_tienda CHAR(4) DEFAULT NULL,
	p_limit INT DEFAULT 10,
	p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 09/10/2023
-- Descripción General: Esta función sirve para obtener los detalles de la
-- estadísticas de un tipo de conciliación.
-- ========================================================================

	RETURNS JSON AS
$BODY$
DECLARE
	idu_corresponsales INT := 1;
	idu_afore INT := 12;
	idu_abc_capital INT := 13;
	idu_conciliacion_correspondiente INT;
BEGIN
	idu_conciliacion_correspondiente := (SELECT idu_conciliacion FROM his_bitacora_conciliaciones WHERE idu_bitacora_conciliacion = p_idu_bitacora_conciliacion);

	CASE
		WHEN idu_corresponsales = idu_conciliacion_correspondiente THEN
			RETURN fun_obtenerdetalleconciliacioncorresponsales(
				p_idu_bitacora_conciliacion,
				p_tipo_archivo,
				p_fecha,
				p_idu_estatus_conciliacion,
				p_busqueda,
				p_tienda,
				p_limit,
				p_offset
			);
		WHEN idu_afore = idu_conciliacion_correspondiente THEN
			RETURN fun_obtenerdetalleconciliacionafore(
				p_idu_bitacora_conciliacion,
				p_fecha,
				p_idu_estatus_conciliacion,
				p_busqueda,
				p_tienda,
				p_limit,
				p_offset
			);
		WHEN idu_abc_capital = idu_conciliacion_correspondiente THEN
			RETURN fun_obtenerdetalleconciliacionabccapital(
				p_idu_bitacora_conciliacion,
				p_fecha,
				p_idu_estatus_conciliacion,
				p_busqueda,
				p_tienda,
				p_limit,
				p_offset
			);
		ELSE
			RETURN JSON_BUILD_OBJECT('error', 'La bitácora de conciliación no posee un tipo de conciliación.', 'publico', true);
	END CASE;
	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al obtener los detalles de la estadísticas de conciliación: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('error', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerdetallesestadisticasconciliaciones (
	p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_tipo_archivo CHAR(3),
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_tienda CHAR(4),
	p_limit INT,
	p_offset INT
) IS 'Esta función sirve para obtener los detalles de la estadísticas de un tipo de conciliación.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener.
p_tipo_archivo= Tipo de archivo a obtener.
p_fecha= Fecha de la bitácora de conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener.
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener.
p_tienda= Tienda a buscar en la bitácora de conciliación a obtener.
p_limit= Límite de registros a obtener.
p_offset= Registro inicial a obtener.';
