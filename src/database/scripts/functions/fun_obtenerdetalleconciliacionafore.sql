CREATE OR REPLACE FUNCTION fun_obtenerdetalleconciliacionafore(
	p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_idu_estatus_conciliacion INT DEFAULT NULL,
	p_busqueda TEXT DEFAULT NULL,
	p_tienda VARCHAR(4) DEFAULT NULL,
	p_limit INT DEFAULT 10,
	p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- de la conciliación de Afore.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
	RETURN (
		SELECT
			JSON_BUILD_OBJECT(
				'total',
				(
					SELECT COUNT(*)
					FROM mov_conciliacion_afore mca
						INNER JOIN cat_estatus_conciliaciones cec ON mca.opc_estatus = cec.idu_estatus_conciliacion
					WHERE mca.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
						AND mca.fec_creacion::DATE = p_fecha
                        AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mca.opc_estatus = p_idu_estatus_conciliacion ELSE TRUE END
                        AND CASE WHEN p_busqueda != '' THEN (
                            TRIM(nom_trabajador)::TEXT ILIKE '%' || p_busqueda || '%'
                            OR TRIM(nom_apellido_pat)::TEXT ILIKE '%' || p_busqueda || '%'
                            OR TRIM(nom_apellido_mat)::TEXT ILIKE '%' || p_busqueda || '%'
                            OR clv_curp::TEXT ILIKE '%' || p_busqueda || '%'
                            OR clv_nss::TEXT ILIKE '%' || p_busqueda || '%'
                            OR imp_neto_pagar::TEXT ILIKE '%' || p_busqueda || '%'
                            OR fol_afore::TEXT ILIKE '%' || p_busqueda || '%'
                            OR idu_conci_afore::TEXT ILIKE '%' || p_busqueda || '%'
                        ) ELSE TRUE END
                        AND CASE WHEN p_tienda != '' THEN num_tienda_solicitud::VARCHAR(4) = p_tienda ELSE TRUE END
				),
				'registros',
				COALESCE(JSON_AGG (
					JSON_BUILD_OBJECT (
						'idu_conci_afore', idu_conci_afore,
						'fecha_alta', fecha_alta::TEXT,
						'nss', clv_nss,
						'curp', clv_curp,
						'nom_trabajador', nom_trabajador,
						'nom_apellido_paterno', nom_apellido_paterno,
						'nom_apellido_materno', nom_apellido_materno,
						'imp_neto_pagar', imp_neto_pagar,
						'fol_afore', fol_afore,
						'num_tienda_solicitud', num_tienda_solicitud,
						'fecha_captura', fecha_captura::TEXT,
						'fecha_pago', fecha_pago::TEXT,
						'hora_pago', hora_pago::TEXT,
						'num_tienda_pago', num_tienda_pago,
                        'clv_forma_pago', clv_forma_pago,
                        'clv_tipo_identificacion', clv_tipo_identificacion,
						'clv_forma_pago', clv_forma_pago,
						'opc_huella_gerente', opc_huella_gerente,
						'num_empleado_autoriza', num_empleado_autoriza,
						'des_referencia_coppel', des_referencia_coppel,
						'num_caja', num_caja,
                        'num_tipo_retiro', num_tipo_retiro,
                        'num_consecutivo_deretiro', num_consecutivo_deretiro,
						'estatus', estatus
					)
				), '[]'),
        'archivo',
        (SELECT SPLIT_PART(nom_archivo, '/', ARRAY_LENGTH(STRING_TO_ARRAY(nom_archivo, '/'), 1)) FROM mov_conciliacion_afore WHERE idu_bitacora_conciliacion = p_idu_bitacora_conciliacion LIMIT 1)
			)
		FROM (
			SELECT
				idu_conci_afore,
				fec_alta as fecha_alta,
				clv_nss,
				clv_curp,
				TRIM(nom_trabajador) as nom_trabajador,
				TRIM(nom_apellido_pat) as nom_apellido_paterno,
				TRIM(nom_apellido_mat) as nom_apellido_materno,
				imp_neto_pagar as imp_neto_pagar,
				fol_afore as fol_afore,
				num_tienda_solicitud as num_tienda_solicitud,
				fec_captura as fecha_captura,
				fec_pago as fecha_pago,
				hrs_pago as hora_pago,
				num_tienda_pago as num_tienda_pago,
                TRIM(clv_tipo_identificacion) as clv_tipo_identificacion,
				TRIM(clv_forma_pago) as clv_forma_pago,
				TRIM(des_huella_gerente) as opc_huella_gerente,
				num_empleado_autoriza as num_empleado_autoriza,
				num_referencia_coppel as des_referencia_coppel,
				num_caja as num_caja,
                num_tipo_retiro,
                num_consecutivo_deretiro,
				cec.des_descripcion AS estatus
			FROM mov_conciliacion_afore mca
				INNER JOIN cat_estatus_conciliaciones cec ON mca.opc_estatus = cec.idu_estatus_conciliacion
			WHERE mca.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
				AND mca.fec_creacion::DATE = p_fecha
				AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mca.opc_estatus = p_idu_estatus_conciliacion ELSE TRUE END
				AND CASE WHEN p_busqueda != '' THEN (
					TRIM(nom_trabajador)::TEXT ILIKE '%' || p_busqueda || '%'
					OR TRIM(nom_apellido_pat)::TEXT ILIKE '%' || p_busqueda || '%'
					OR TRIM(nom_apellido_mat)::TEXT ILIKE '%' || p_busqueda || '%'
					OR clv_curp::TEXT ILIKE '%' || p_busqueda || '%'
					OR clv_nss::TEXT ILIKE '%' || p_busqueda || '%'
					OR imp_neto_pagar::TEXT ILIKE '%' || p_busqueda || '%'
					OR fol_afore::TEXT ILIKE '%' || p_busqueda || '%'
					OR idu_conci_afore::TEXT ILIKE '%' || p_busqueda || '%'
				) ELSE TRUE END
				AND CASE WHEN p_tienda != '' THEN num_tienda_solicitud::VARCHAR(4) = p_tienda ELSE TRUE END
			ORDER BY idu_conci_afore
			LIMIT p_limit
			OFFSET p_offset
		) consulta
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerdetalleconciliacionafore(
	p_idu_bitacora_conciliacion INT,
	p_fecha DATE,
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_tienda VARCHAR,
	p_limit INT,
	p_offset INT
) IS ' Esta función sirve para obtener las estadísticas de la conciliación de Afore.
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener,
p_fecha= Fecha de la bitácora de conciliación a obtener,
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener,
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener,
p_tienda= Tienda a buscar en la bitácora de conciliación a obtener,
p_limit= Límite de registros a obtener,
p_offset= Registro inicial a obtener';
