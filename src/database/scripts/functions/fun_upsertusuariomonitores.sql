CREATE OR REPLACE FUNCTION fun_upsertusuariomonitores(
	p_idu_perfil INT DEFAULT NULL,
	p_num_empleado INT DEFAULT NULL,
	p_nom_empleado TEXT DEFAULT NULL,
	p_nom_puesto TEXT DEFAULT NULL,
	p_num_telefono VARCHAR(10) DEFAULT NULL,
    p_opc_estatus BOOLEAN DEFAULT NULL,
    p_idu_usuario INT DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 18/12/2023
-- Descripción General: Esta función inserta o actualiza, si mandas el
-- identificador, un registro en la cat_usuario_monitores.
-- ========================================================================

	RETURNS JSON AS

$BODY$
DECLARE
  esActualizacion BOOLEAN := p_idu_usuario IS NOT NULL;
  esDuplicado BOOLEAN := FALSE;
  fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
  registro_insertado INT := 0;
BEGIN
	esDuplicado := EXISTS (
        SELECT 1
        FROM cat_usuario_monitores
        WHERE  LOWER(nom_empleado) = LOWER(p_nom_empleado)
            OR num_empleado = p_num_empleado
            AND opc_activo
            AND CASE WHEN esActualizacion THEN idu_usuario <> p_idu_usuario ELSE TRUE END
    );

    IF esDuplicado THEN
        RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'El empleado ya se encuentra registrado, favor de verificarlo');
    END IF;

	IF esActualizacion THEN -- Actualizar
        UPDATE cat_usuario_monitores
        SET idu_perfil = COALESCE(p_idu_perfil, idu_perfil),
            opc_estatus = COALESCE(p_opc_estatus, opc_estatus),
            fec_actualizacion = fecha
        WHERE idu_usuario = p_idu_usuario;
	ELSE -- Insertar
	    INSERT INTO cat_usuario_monitores (
		    idu_perfil,
		    num_empleado,
		    nom_empleado,
            nom_puesto,
            num_telefono,
            opc_estatus,
		    fec_registro
	    ) VALUES (
            p_idu_perfil,
            p_num_empleado,
            p_nom_empleado,
            p_nom_puesto,
            p_num_telefono,
            p_opc_estatus,
            fecha
	    )
	    RETURNING idu_usuario INTO p_idu_usuario; -- Recupera el nuevo idu_usuario generado
	END IF;

	-- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente' ELSE 'Registro insertado correctamente' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsertusuariomonitores(
	p_idu_perfil INT,
	p_num_empleado INT,
	p_nom_empleado TEXT,
	p_nom_puesto TEXT,
	p_num_telefono VARCHAR(10),
    p_opc_estatus BOOLEAN,
    p_idu_usuario INT
) IS 'Esta función inserta o actualiza, si mandas el identificador, un registro en la cat_usuario_monitores
p_idu_perfil= Identificador del perfil
p_num_empleado= Número de empleado
p_nom_empleado= Nombre del empleado
p_nom_puesto= Nombre del puesto
p_num_telefono= Número de teléfono
p_opc_estatus= Estatus del usuario
p_idu_usuario= Identificador del usuario';
