DROP FUNCTION IF EXISTS fun_obtenerarchivoscargados(INT, INT, DATE);
CREATE OR REPLACE FUNCTION fun_obtenerarchivoscargados(pagina INT, numRegistros INT, fecha DATE)
RETURNS TABLE (
   idu_cbanco_archivo	INTEGER,
   nom_archivo     		CHAR(20),
   fec_archivo     		DATE,
   num_movimientos		INTEGER,
   imp_archivo     		INTEGER,
   fec_registro         DATE,
   count                BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
   RETURN QUERY
   SELECT 
      arc.idu_cbanco_archivo,
      arc.nom_archivo,
      arc.fec_archivo,
      arc.num_movimientos,
      arc.imp_archivo,
      arc.fec_registro,
      COUNT(*) OVER() AS count
   FROM ctl_cbanco_archivos arc
      WHERE arc.fec_registro = COALESCE(fecha, arc.fec_registro) :: DATE
      ORDER BY arc.fec_registro DESC
      LIMIT numRegistros OFFSET (pagina - 1) * numRegistros;
	 
END
$func$;
