CREATE OR REPLACE FUNCTION fun_obtenercatalogoestatus()
	RETURNS TABLE (
		idu_estatus_conciliacion INT,
		nombre TEXT
	) AS

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 04/10/2023
-- Descripción General: Esta función sirve para obtener el catálogo de
-- estatus.
-- ========================================================================

$BODY$
BEGIN
  RETURN QUERY
	SELECT
		cec.idu_estatus_conciliacion,
		cec.des_descripcion as nombre
	FROM
		cat_estatus_conciliaciones cec
	WHERE
		cec.opc_estatus = b'1';
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenercatalogoestatus() IS 'Esta función sirve para obtener el catálogo de estatus.';
