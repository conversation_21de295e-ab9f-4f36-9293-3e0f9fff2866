CREATE OR REPLACE FUNCTION public.fun_obtenerdestinatario(
    p_idu_correo_destinatario INT
)

-- =============================================
-- Autor:               Gaman Solutions
-- Fecha:               02/01/2024
-- Descripción General: Esta función sirve para obtener un destinatario de correo de conciliacón por id
-- =============================================

RETURNS JSON AS
$BODY$
BEGIN
    RETURN (
		SELECT
            JSON_BUILD_OBJECT(
                'idu_correo_destinatario', d.idu_correo_destinatario,
                'nom_destinatario', d.nom_destinatario,
                'nom_conciliacion', co.nom_conciliacion,
                'des_destinatario', d.des_destinatario,
                'opc_estatus', CASE WHEN d.opc_estatus = '1' THEN TRUE ELSE FALSE END,
                'tipo', CASE WHEN c.opc_alerta = '1' THEN 'Soporte' ELSE 'Usuario' END
            )
        FROM
            cat_conciliacion_correo_destinatarios d
        INNER JOIN cat_conciliacion_correos c ON d.idu_correo = c.idu_correo
        INNER JOIN cat_conciliaciones co ON c.idu_conciliacion = co.idu_conciliacion
        WHERE
            d.idu_correo_destinatario = p_idu_correo_destinatario
	);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION public.fun_obtenerdestinatario(
    p_iduconciliacion INT
) IS 'Esta función sirve para obtener un destinatario por su idu';
