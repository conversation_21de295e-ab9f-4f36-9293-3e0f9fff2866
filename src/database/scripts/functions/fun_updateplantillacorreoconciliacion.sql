CREATE OR REPLACE FUNCTION fun_updateplantillacorreoconciliacion(
    p_idu_correo INT,
    p_des_asunto TEXT,
    p_des_cuerpo TEXT
)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 03/01/2024
-- Descripción General: Esta función actualiza la plantilla de correo de conciliacion
-- ========================================================================
    RETURNS JSON AS
$BODY$
DECLARE
    registro_insertado INT := 0;
BEGIN
    UPDATE cat_conciliacion_correos
    SET des_asunto = p_des_asunto,
        des_cuerpo = p_des_cuerpo
    WHERE idu_correo = p_idu_correo;

    -- Si la inserción se realiza correctamente, establecer el valor en TRUE
    GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', 'Registro actualizado correctamente');
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', 'Error al actualizar el registro');
    END IF;

    EXCEPTION -- Capturar y manejar errores
        WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
    RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'Error al actualizar el registro');
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_updateplantillacorreoconciliacion(
    p_idu_correo INT,
    p_des_asunto TEXT,
    p_des_cuerpo TEXT
) IS 'Esta función actualiza una plantilla de correo
    p_idu_correo=Idu de la de la plantilla
    p_des_asunto=Asunto de la plantilla a actualizar
    p_des_cuerpo=Cuerpo de la plantilla a actualizar
';






