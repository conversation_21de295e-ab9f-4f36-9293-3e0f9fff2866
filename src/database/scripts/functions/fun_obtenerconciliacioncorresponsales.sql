CREATE OR REPLACE FUNCTION fun_obtenerconciliacioncorresponsales(
	p_fecha DATE DEFAULT CURRENT_TIMESTAMP,
	p_estatus INT DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/10/2023
-- Descripción General: Esta función sirve para obtener las estadísticas de
-- la conciliación de corresponsales.
-- ========================================================================

	RETURNS TABLE (
		idu_bitacora_conciliacion INT,
		fecha TEXT,
		archivo_origen TEXT,
		descripcion TEXT,
		registros_origen BIGINT,
		incidencias BIGINT,
		estatus TEXT
	) AS
$BODY$
DECLARE
	descripcion_CCP CONSTANT TEXT := 'Contiene los depositos y retiros con tarjeta de debito';
	descripcion_CCD CONSTANT TEXT := 'Contiene los depositos y retiros con tarjeta de credito';
	descripcion_TPD CONSTANT TEXT := 'Contiene los movimientos de transferencias bancoppel';
	descripcion_DIF CONSTANT TEXT := 'Contiene los folios que banco detecto con algun detalle';
BEGIN
	RETURN QUERY
	SELECT
		hbc.idu_bitacora_conciliacion AS idu_bitacora_conciliacion,
		hbc.fec_conciliacion::DATE::TEXT AS fecha,
		CONCAT(mcc.des_archivo, '_', p_fecha, '.txt') AS archivo_origen,
		(
			CASE
				WHEN mcc.des_archivo = 'CCP' THEN descripcion_CCP
				WHEN mcc.des_archivo = 'CCD' THEN descripcion_CCD
				WHEN mcc.des_archivo = 'TPD' THEN descripcion_TPD
				WHEN mcc.des_archivo = 'DIF' THEN descripcion_DIF
				ELSE '- - -'
			END
		) AS descripcion,
		COUNT(mcc.idu_conciliacion_corresponsal) AS registros_origen,
		SUM(CASE WHEN mcc.idu_estatus_conciliacion = 3 THEN 1 ELSE 0 END) AS incidencias,
		(CASE WHEN(SUM(CASE WHEN mcc.idu_estatus_conciliacion = 2 THEN 0 ELSE 1 END) > 0) THEN 'Diferencias' ELSE 'Exitoso' END) AS estatus
	FROM his_bitacora_conciliaciones hbc
		INNER JOIN mov_conciliacion_corresponsales mcc ON hbc.idu_bitacora_conciliacion = mcc.idu_bitacora_conciliacion
	WHERE hbc.fec_conciliacion = p_fecha
	GROUP BY hbc.idu_bitacora_conciliacion, mcc.des_archivo
	HAVING CASE WHEN p_estatus IS NOT NULL THEN (CASE WHEN(SUM(CASE WHEN mcc.idu_estatus_conciliacion = 2 THEN 0 ELSE 1 END) > 0) THEN 0 ELSE 1 END) = p_estatus ELSE TRUE END;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_obtenerconciliacioncorresponsales(
  p_fecha DATE,
  p_estatus INT
) IS 'Esta función sirve para obtener las estadísticas de la conciliación de corresponsales.
p_fecha: Fecha de la conciliación.
p_estatus: Estatus de la conciliación.';
