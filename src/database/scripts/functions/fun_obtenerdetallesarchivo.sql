CREATE OR REPLACE FUNCTION fun_obtenerdetallesarchivo(idu_archivo INT, pagina INT, numRegistros INT)
RETURNS TABLE (
   clv_rpu				CHAR(21),
   imp_detalle		INTEGER,
	fec_movimiento	DATE,
   count					BIGINT
)
LANGUAGE plpgsql AS
$func$
BEGIN
   RETURN QUERY
   SELECT  det.clv_rpu, det.imp_detalle, det.fec_movimiento, COUNT(*) OVER() AS count
   FROM ctl_cbanco_archivo_detalles det
	 WHERE idu_cbanco_archivo = idu_archivo
	 LIMIT numRegistros OFFSET (pagina - 1) * numRegistros;
	 
END
$func$;
