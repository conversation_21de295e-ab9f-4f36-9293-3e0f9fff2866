CREATE OR REPLACE FUNCTION fun_obtenerdetalleconciliacioncorresponsales(
	p_idu_bitacora_conciliacion INT,
	p_tipo_archivo CHAR(3),
	p_fecha DATE,
	p_idu_estatus_conciliacion INT DEFAULT NULL,
	p_busqueda TEXT DEFAULT NULL,
	p_tienda CHAR(4) DEFAULT NULL,
	p_limit INT DEFAULT 10,
	p_offset INT DEFAULT 0
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 07/11/2023
-- Descripción General: Esta función sirve para obtener las estadísticas
-- de la conciliación de Corresponsales.
-- ========================================================================

	RETURNS JSON AS
$BODY$
BEGIN
  IF p_tipo_archivo IS NULL OR p_tipo_archivo = '' THEN
    RAISE EXCEPTION 'Debe especificar el tipo de archivo.';
  END IF;

	RETURN
		 JSON_BUILD_OBJECT (
			'total',
			(
				SELECT COUNT(*)
				FROM mov_conciliacion_corresponsales mcc
					INNER JOIN cat_estatus_conciliaciones cec ON mcc.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
				WHERE
					mcc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
					AND mcc.fec_fecha = p_fecha
					AND mcc.des_archivo = p_tipo_archivo
					AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mcc.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
					AND CASE WHEN p_busqueda != '' THEN (des_monto::TEXT ILIKE '%' || p_busqueda || '%' OR num_folio::TEXT ILIKE '%' || p_busqueda || '%' OR num_transaccion::TEXT ILIKE '%' || p_busqueda || '%') ELSE TRUE END
					AND CASE WHEN p_tienda != '' THEN substring(num_folio::text, -4)::CHAR(4) = p_tienda ELSE TRUE END
			),
			'registros',
			COALESCE(JSON_AGG (
				JSON_BUILD_OBJECT (
					'tipo_transaccion', tipo_transaccion,
					'folio_bancoppel', folio_bancoppel,
					'importe', importe,
					'tienda', tienda,
					'estatus', estatus
				)
			), '[]'),
      'archivo',
      (SELECT (p_tipo_archivo || '_' || fec_fecha || '.csv') FROM mov_conciliacion_corresponsales WHERE idu_bitacora_conciliacion = p_idu_bitacora_conciliacion LIMIT 1)
		)
	FROM (
		SELECT
			num_transaccion as tipo_transaccion,
			num_folio as folio_bancoppel,
			des_monto as importe,
			substring(num_folio::text, -4)::CHAR(4) as tienda,
			cec.des_descripcion as estatus
		FROM mov_conciliacion_corresponsales mcc
		INNER JOIN cat_estatus_conciliaciones cec ON mcc.idu_estatus_conciliacion = cec.idu_estatus_conciliacion
		WHERE
			mcc.idu_bitacora_conciliacion = p_idu_bitacora_conciliacion
			AND mcc.fec_fecha = p_fecha
			AND mcc.des_archivo = p_tipo_archivo
			AND CASE WHEN p_idu_estatus_conciliacion IS NOT NULL THEN mcc.idu_estatus_conciliacion = p_idu_estatus_conciliacion ELSE TRUE END
			AND CASE WHEN p_busqueda != '' THEN (des_monto::TEXT ILIKE '%' || p_busqueda || '%' OR num_folio::TEXT ILIKE '%' || p_busqueda || '%' OR num_transaccion::TEXT ILIKE '%' || p_busqueda || '%') ELSE TRUE END
			AND CASE WHEN p_tienda != '' THEN substring(num_folio::text, -4)::CHAR(4) = p_tienda ELSE TRUE END
		ORDER BY num_folio
		LIMIT p_limit
		OFFSET p_offset
	) consulta;
END;
$BODY$
LANGUAGE plpgsql;

COMMENT ON FUNCTION fun_obtenerdetalleconciliacioncorresponsales(
	p_idu_bitacora_conciliacion INT,
	p_tipo_archivo CHAR(3),
	p_fecha DATE,
	p_idu_estatus_conciliacion INT,
	p_busqueda TEXT,
	p_tienda CHAR(4),
	p_limit INT,
	p_offset INT
) IS 'Esta función sirve para obtener las estadísticas de la conciliación de Corresponsales..
p_idu_bitacora_conciliacion= Identificador de la bitácora de conciliación a obtener.
p_tipo_archivo= Tipo de archivo a obtener.
p_fecha= Fecha de la bitácora de conciliación a obtener.
p_idu_estatus_conciliacion= Identificador del estatus de la bitácora de conciliación a obtener.
p_busqueda= Texto a buscar en la bitácora de conciliación a obtener.
p_tienda= Tienda a buscar en la bitácora de conciliación a obtener.
p_limit= Límite de registros a obtener.
p_offset= Registro inicial a obtener.';
