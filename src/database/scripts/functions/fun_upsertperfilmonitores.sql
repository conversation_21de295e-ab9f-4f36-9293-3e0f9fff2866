CREATE OR REPLACE FUNCTION fun_upsertperfilmonitores(
	p_nombre VARCHAR(50) DEFAULT NULL,
	p_descripcion TEXT DEFAULT NULL,
	p_estatus BOOLEAN DEFAULT NULL,
	p_modulos INT[] DEFAULT NULL,
    p_permisos_ejecucion INT[] DEFAULT NULL,
	p_idu_perfil INT DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 20/10/2023
-- Descripción General: Esta función inserta o actualiza, si mandas el
-- identificador, un registro en la cat_perfil_monitores.
-- ========================================================================

	RETURNS JSON AS

$BODY$
DECLARE
    esActualizacion BOOLEAN := p_idu_perfil IS NOT NULL;
    esDuplicado BOOLEAN := FALSE;
    fecha TIMESTAMP WITHOUT TIME ZONE := CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE;
    registro_insertado INT := 0;
    tieneModulos BOOLEAN := p_modulos IS NOT NULL;
    tienePermisos BOOLEAN := p_permisos_ejecucion IS NOT NULL;
BEGIN
	esDuplicado := EXISTS (
        SELECT 1
        FROM cat_perfil_monitores
        WHERE LOWER(nom_perfil) = LOWER(p_nombre)
            AND opc_activo
            AND CASE WHEN esActualizacion THEN idu_perfil <> p_idu_perfil ELSE TRUE END
    );

    IF esDuplicado THEN
        RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'El nombre de perfil no se encuentra disponible, intente con otro.');
    END IF;

	IF esActualizacion THEN -- Actualizar
        UPDATE cat_perfil_monitores
        SET nom_perfil = COALESCE(p_nombre, nom_perfil),
            des_perfil = COALESCE(p_descripcion, des_perfil),
            opc_estatus = COALESCE(p_estatus, opc_estatus),
            fec_actualizacion = fecha
        WHERE idu_perfil = p_idu_perfil;
	ELSE -- Insertar
        INSERT INTO cat_perfil_monitores (
            nom_perfil,
            des_perfil,
            opc_estatus,
            fec_registro
        ) VALUES (
            p_nombre,
            p_descripcion,
            p_estatus,
            fecha
        )
        RETURNING idu_perfil INTO p_idu_perfil; -- Recupera el nuevo idu_perfil generado
	END IF;

	IF tieneModulos THEN
        -- Actualiza los registros que coincidan en p_modulos con los valores del arreglo
        UPDATE cat_permiso_monitores
            SET opc_activo = FALSE
        WHERE idu_perfil = p_idu_perfil AND idu_modulo NOT IN ( SELECT UNNEST ( p_modulos::int[] ) );

        -- Inserta registros que no existan en la tabla
        IF COALESCE(ARRAY_LENGTH(p_modulos, 1), 0) > 0 THEN
            INSERT INTO cat_permiso_monitores (idu_perfil, idu_modulo)
                SELECT p_idu_perfil, unnest(p_modulos::int[])
            ON CONFLICT (idu_perfil, idu_modulo)
            DO UPDATE SET opc_activo = TRUE;
        END IF;
	END IF;

    IF tienePermisos THEN
        -- Actualiza los registros que coincidan en p_permisos_ejecucion con los valores del arreglo
        UPDATE cat_perfil_permiso_ejecucion_manual_monitores
            SET opc_activo = FALSE
        WHERE idu_perfil = p_idu_perfil AND idu_conciliacion NOT IN ( SELECT UNNEST ( p_permisos_ejecucion::INT[] ) );

        -- Inserta registros que no existan en la tabla
        IF COALESCE(ARRAY_LENGTH(p_permisos_ejecucion, 1), 0) > 0 THEN
            INSERT INTO cat_perfil_permiso_ejecucion_manual_monitores (idu_perfil, idu_conciliacion)
                SELECT p_idu_perfil, unnest(p_permisos_ejecucion::INT[])
            ON CONFLICT (idu_conciliacion, idu_perfil)
            DO UPDATE SET opc_activo = TRUE;
        END IF;
	END IF;

	-- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

	-- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente' ELSE 'Registro insertado correctamente' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro' ELSE 'Error al insertar el registro' END);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsertperfilmonitores(
	p_nombre VARCHAR(50),
    p_descripcion TEXT,
    p_estatus BOOLEAN,
	p_modulos INT[],
    p_permisos_ejecucion INT[],
	p_idu_perfil INT
) IS 'Esta función inserta o actualiza, si mandas el identificador, un registro en la cat_perfil_monitores
p_nombre= Nombre del perfil
p_descripcion= Descripción del perfil
p_estatus= Estatus del perfil
p_modulos= Arreglo de identificadores de módulos
p_permisos_ejecucion= Arreglo de identificadores de permisos de ejecución
p_idu_perfil= Identificador del perfil a actualizar';
