CREATE TABLE cat_modulo_proceso_ejecucion_manual_monitores (
    idu_modulo INT NOT NULL DEFAULT 0,
    idu_conciliacion INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_modulo_proceso_ejecucion_manual_monitores PRIMARY KEY (idu_modulo, idu_conciliacion),
    CONSTRAINT fk_cat_modulo_proceso_ejecucion_manual_monitores_cat_conciliaciones FOREIGN KEY (idu_conciliacion) REFERENCES cat_conciliaciones (idu_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_modulo_proceso_ejecucion_manual_monitores_cat_modulo_monitores FOREIGN KEY (idu_modulo) REFERENCES cat_modulo_monitores (idu_modulo) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);
