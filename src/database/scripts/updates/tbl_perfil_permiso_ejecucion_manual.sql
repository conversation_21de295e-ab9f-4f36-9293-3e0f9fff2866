CREATE TABLE cat_perfil_permiso_ejecucion_manual_monitores (
    idu_conciliacion INT NOT NULL DEFAULT 0,
    idu_perfil INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_perfil_permiso_ejecucion_manual_monitores PRIMARY KEY (idu_conciliacion, idu_perfil),
    CONSTRAINT fk_cat_perfil_permiso_ejecucion_manual_monitores_cat_conciliaciones FOREIGN KEY (idu_conciliacion) REFERENCES cat_conciliaciones (idu_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_perfil_permiso_ejecucion_manual_monitores_cat_perfil_monitores FOREIGN KEY (idu_perfil) REFERENCES cat_perfil_monitores (idu_perfil) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);
