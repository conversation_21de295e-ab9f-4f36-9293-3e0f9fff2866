CREATE TABLE cat_usuario_permiso_ejecucion_manual_monitores (
    idu_conciliacion INT NOT NULL DEFAULT 0,
    idu_usuario INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_permiso_ejecucion_manual_monitores PRIMARY KEY (idu_conciliacion, idu_usuario),
    CONSTRAINT fk_cat_permiso_ejecucion_manual_monitores_cat_conciliaciones FOREIGN KEY (idu_conciliacion) REFERENCES cat_conciliaciones (idu_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_permiso_ejecucion_manual_monitores_cat_usuario_monitores FOREIGN KEY (idu_usuario) REFERENCES cat_usuario_monitores (idu_usuario) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);
