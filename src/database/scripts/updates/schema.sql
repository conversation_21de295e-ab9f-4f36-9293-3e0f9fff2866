CREATE TABLE cat_modulo_proceso_ejecucion_manual_monitores (
    idu_modulo INT NOT NULL DEFAULT 0,
    idu_conciliacion INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_modulo_proceso_ejecucion_manual_monitores PRIMARY KEY (idu_modulo, idu_conciliacion),
    CONSTRAINT fk_cat_modulo_proceso_ejecucion_manual_monitores_cat_conciliaciones FOREIGN KEY (idu_conciliacion) REFERENCES cat_conciliaciones (idu_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_modulo_proceso_ejecucion_manual_monitores_cat_modulo_monitores FOREIGN KEY (idu_modulo) REFERENCES cat_modulo_monitores (idu_modulo) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE cat_perfil_permiso_ejecucion_manual_monitores (
    idu_conciliacion INT NOT NULL DEFAULT 0,
    idu_perfil INT NOT NULL DEFAULT 0,
    opc_activo BOOLEAN NOT NULL DEFAULT TRUE,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT pk_cat_perfil_permiso_ejecucion_manual_monitores PRIMARY KEY (idu_conciliacion, idu_perfil),
    CONSTRAINT fk_cat_perfil_permiso_ejecucion_manual_monitores_cat_conciliaciones FOREIGN KEY (idu_conciliacion) REFERENCES cat_conciliaciones (idu_conciliacion) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION,
    CONSTRAINT fk_cat_perfil_permiso_ejecucion_manual_monitores_cat_perfil_monitores FOREIGN KEY (idu_perfil) REFERENCES cat_perfil_monitores (idu_perfil) MATCH SIMPLE ON UPDATE NO ACTION ON DELETE NO ACTION
);

CREATE TABLE his_monitor_conciliacion_registro_transacciones (
    idu UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    num_empleado VARCHAR(8) NOT NULL,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE :: TIMESTAMP,
    des_estatus VARCHAR(10) CHECK (des_estatus IN ('Exito', 'Fallo')),
    nom_recurso VARCHAR(100) NOT NULL,
    nom_rol VARCHAR(50) NOT NULL,
    nom_tipo_transaccion VARCHAR(20) NOT NULL,
    clv_ip INET NOT NULL
);

CREATE TABLE his_monitor_conciliacion_registro_seguridad (
    idu UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE :: TIMESTAMP,
    des_estatus VARCHAR(10) CHECK (des_estatus IN ('Exito', 'Fallo')),
    des_transaccion_realizada VARCHAR(250),
    clv_ip INET NOT NULL
);