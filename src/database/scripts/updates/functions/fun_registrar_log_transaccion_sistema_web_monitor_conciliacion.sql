CREATE OR REPLACE FUNCTION fun_registrar_log_transaccion_sistema_web_monitor_conciliacion(
    p_num_empleado VARCHAR(8),
    p_des_estatus BOOLEAN,
    p_nom_recurso VARCHAR(100),
    p_nom_tipo_transaccion VARCHAR(10),
    p_clv_ip INET,
    p_rol VARCHAR(50)
)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 01/04/2025
-- Descripcion General: Funcion que inserta un registro en la tabla de his_monitor_conciliacion_registro_transacciones.
-- ========================================================================
RETURNS JSON AS
$BODY$
DECLARE
    registro_insertado INT := 0;
BEGIN
    INSERT INTO his_monitor_conciliacion_registro_transacciones (
        num_empleado,
        des_estatus,
        nom_recurso,
        nom_tipo_transaccion,
        clv_ip,
        nom_rol
    ) VALUES (
        p_num_empleado,
        CASE WHEN p_des_estatus THEN 'Exito' ELSE 'Fallo' END,
        p_nom_recurso,
        p_nom_tipo_transaccion,
        p_clv_ip,
        p_rol
    );

	-- Si la insercion se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', 'Registro insertado correctamente.');
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', 'Error al insertar el registro.');
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'Error al insertar el registro.');
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_registrar_log_transaccion_sistema_web_ingresos(
    p_num_empleado VARCHAR(8),
    p_des_estatus BOOLEAN,
    p_nom_recurso VARCHAR(100),
    p_nom_tipo_transaccion VARCHAR(10),
    p_clv_ip INET,
    p_rol VARCHAR(50)
) IS 'Funcion que inserta un registro en la tabla de his_monitor_conciliacion_registro_transacciones.
p_num_empleado = Identificador del usuario
p_des_estatus = Estatus del resultado de la transaccion
p_nom_recurso = Nombre de la tabla
p_nom_tipo_transaccion = Tipo de la transaccion que fue ejecutada
p_clv_ip = Ip del origen
p_rol = Rol del usuario
';
