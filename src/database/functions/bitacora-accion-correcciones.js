/* eslint-disable no-unused-vars */
import * as db from '@postgresql';

/**
 * Función para insertar o actualizar una acción o correción a las bitacoras
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} parametros.iduBitacoraConciliacion Identificador de la bitacora de conciliación
 * @param {Number} parametros.iduEstatusConciliacion Identificador del estatus
 * @param {Number} parametros.personaInspeccion Nombre del inspector
 * @param {String} parametros.observacion Observación
 * @param {Number} [parametros.iduBitacoraAccionCorreccion] Identificador de la bitacora de
 *  acción o corrección
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: { estatus: Boolean mensaje?: String } }>
 * } Promesa con el resultado de la consulta
 */
export function registrarActualizarAccionCorreccion({
  iduBitacoraConciliacion = 0,
  iduEstatusConciliacion,
  personaInspeccion,
  observacion,
  iduBitacoraAccionCorreccion,
}) {
  const parametros = [
    iduBitacoraConciliacion,
    iduEstatusConciliacion,
    personaInspeccion,
    observacion,
  ];

  if (iduBitacoraAccionCorreccion) parametros.push(iduBitacoraAccionCorreccion);
  else parametros.push(null);

  const query = 'SELECT fun_upsertaccioncorreccion($1, $2, $3, $4, $5) as resultado';

  return db.query(query, parametros);
}

/**
 * Función para obtener listado de la bitacora de acciones y correcciones de una conciliación
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} parametros.iduBitacoraConciliacion Identificador de la bitacora de conciliación
 * @param {Number} [parametros.iduEstatusConciliacion] Identificador del estatus
 * @param {String} [parametros.busqueda] Término a buscar
 * @param {Number} [parametros.registrosPorPagina] Registros por página
 * @param {Number} [parametros.pagina] Página
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: {
 *    total: Number
 *    registros: {
 *      idu_bitacora_accion_correccion: Number
 *      estatus: String
 *      persona_inspeccion: String
 *      observacion: String
 *      fecha_registro: String
 *      fecha_actualizacion: String
 *    }[]
 *  }
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerBitacoraAccionCorrecciones({
  iduBitacoraConciliacion,
  iduEstatusConciliacion = null,
  busqueda = '',
  registrosPorPagina = 10,
  pagina = 1,
}) {
  const parametros = [
    iduBitacoraConciliacion,
    iduEstatusConciliacion,
    busqueda,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
  ];

  const query = 'SELECT fun_obtenerbitacoraaccioncorrecciones($1, $2, $3, $4, $5) as resultado';

  return db.query(query, parametros);
}
