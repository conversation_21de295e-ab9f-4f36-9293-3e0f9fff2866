/* eslint-disable no-unused-vars */
import * as JsonToXML from 'js2xmlparser';
import * as db from '@postgresql';

/**
 * Función para guardar la información de un archivo.
 * @param {String} archivoJson Contenido del archivo en formato JSON.
 * @returns {Promise<import('pg').QueryResult<{ resultado: int }>} Promesa
 *  con el resultado de la consulta
 */
export function guardarArchivo(archivoJson) {
  const xmlArchivo = JsonToXML.parse('archivos', archivoJson).replace("<?xml version='1.0'?>", '');
  const parametros = [xmlArchivo];
  const query = 'SELECT fun_guardararchivocbanco($1) as resultado';
  return db.query(query, parametros);
}

/**
 * Función que actualiza los registros de un archivo.
 * @param {Date} fecha Fecha del archivo a actualizar.
 * @returns {Promise} Promesa con el resultado de la ejecución.
 */
export function actualizarArchivo(fecha) {
  const parametros = [fecha];
  const query = 'SELECT fun_actualizararchivocbanco($1) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para actualizar la información de un archivo.
 * @param {String} archivoJson Contenido del archivo en formato JSON.
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function obtenerContenidoArchivo(archivoId) {
  const parametros = [archivoId];
  const query = 'SELECT linea FROM fun_obtenercontenidoarchivo($1);';

  return db.query(query, parametros);
}

/**
 * Función que obtiene los registros cfe de manera paginada.
 * @param {Number} pagina Número de página
 * @param {Number} numRegistros Número de registros a mostrar.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerRegistrosCfe({
  pagina, numRegistros,
}) {
  const parametros = [pagina, numRegistros];
  const query = `
    SELECT
      tienda,
      fecha,
      caja,
      contrato,
      importe,
      recibo,
      count
    FROM
      fun_obtenerregistroscfe($1, $2)`;

  return db.query(query, parametros);
}

/**
 * Función que obtiene un archivo por fecha.
 * @param {Date} fecha Fecha del archivo para buscar.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerArchivoFecha(fecha) {
  const parametros = [fecha];
  const query = `
    SELECT
      idu_cbanco_archivo,
      nom_archivo,
      fec_archivo,
      num_movimientos,
      imp_archivo,
      count
    FROM
      fun_obtenerarchivofecha($1)
  `;

  return db.query(query, parametros);
}

/**
 * Función que obtiene todos los archivos cargados.
 * @param {Number} pagina Número de página
 * @param {Number} numRegistros Número de registros a mostrar.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerArchivosCargados({
  pagina,
  numRegistros,
  fecha,
}) {
  const parametros = [pagina, numRegistros, fecha];
  const query = `
    SELECT
      idu_cbanco_archivo,
      nom_archivo,
      fec_archivo,
      num_movimientos,
      imp_archivo,
      fec_registro,
      count
    FROM
      fun_obtenerarchivoscargados($1, $2, $3)
  `;

  return db.query(query, parametros);
}

/**
 * Función que obtiene los detalles de un archivo.
 * @param {Number} pagina Número de página
 * @param {Number} numRegistros Número de registros a mostrar.
 * @param {Number} archivoId Identificador del archivo de la consulta.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerDetallesArchivo({
  archivoId,
  pagina,
  numRegistros,
}) {
  const parametros = [archivoId, pagina, numRegistros];
  const query = `
    SELECT
      clv_rpu,
      fec_movimiento,
      imp_detalle,
      count
    FROM
      fun_obtenerdetallesarchivo($1, $2, $3)
  `;

  return db.query(query, parametros);
}

/**
 * Función que carga los datos de un archivo a la BD
 * @param {Date} fecha Fecha del archivo para cargar.
 * @param {Boolean} reemplazar Bandera para reemplazar los registros
 * @returns {Promise} Promesa con el resultado de la ejecución.
 */
export function cargarArchivo(fecha, reemplazar) {
  const parametros = [fecha, reemplazar];
  const query = 'SELECT fun_agregar_archivo_cbanco_tmp($1, $2) as resultado';
  return db.query(query, parametros);
}
