import * as db from '@postgresql';

/**
 * Verifica si un empleado tiene acceso al sistema
 *
 * @param {number} numEmpleado Número de empleado
 * @returns {Promise<import('pg').QueryResult<{ resultado: boolean | null }>} Promesa
 *  con el resultado de la consulta
 */
export function obtenerEmpleadoPorId(numEmpleado) {
  const query = 'SELECT fun_autenticarempleadomonitor($1) as resultado';
  return db.query(query, [numEmpleado]);
}
