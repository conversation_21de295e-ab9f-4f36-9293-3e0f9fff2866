/* eslint-disable no-unused-vars */
import * as db from '@postgresql';

/**
 * Función para guardar la información de un archivo.
 * @param {Number} iduConciliacion Identificador de la conciliación.
 * @param {String} mensaje Motivo del bloqueo.
 * @param {String} fecBloqueo Fecha para la cual será el bloqueo.
 * @param {String} iduUsuario Usuario que crea el bloqueo.
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function guardarConcilicacionBloqueo({
  idu_conciliacion,
  des_mensaje_bitacora,
  fec_bloqueo,
  idu_usuario_registro,
}) {
  const parametros = [idu_conciliacion, des_mensaje_bitacora, fec_bloqueo, idu_usuario_registro];
  const query = 'SELECT fun_guardarconciliacionbloqueo($1, $2, $3, $4) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para actualizar la información de un archivo.
 * @param {String} iduConciliacion Identificador de la conciliación.
 * @param {String} pagina Número de página.
 * @param {String} numRegistros Total de registros a mostrar.
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function obtenerBloqueosPorConciliacion({
  iduConciliacion,
  pagina,
  numRegistros,
}) {
  const parametros = [iduConciliacion, pagina, numRegistros];
  const query = `
  SELECT
    idu_conciliacion_bloqueo,
    fec_bloqueo,
    fec_registro,
    idu_usuario_registro,
    nom_empleado,
    idu_estatus_conciliacion,
    des_estatus,
    count
  FROM
    fun_obtenerbloqueosporconciliacion($1, $2, $3)
  `;
  return db.query(query, parametros);
}

/**
 * Función para actualizar la información de un archivo.
 * @param {Number} iduBloqueo Identificador del bloqueo.
 * @param {Number} iduConciliacion Identificador de la conciliación.
 * @param {String} mensaje Motivo de la cancelación.
 * @param {Date} fecBloqueo Fecha del bloqueo.
 * @param {Boolean} opcEstatus Boolean que indica si el registro está activo.
 * @returns {Promise<import('pg').QueryResult<{ respuesta: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function actualizarConciliacionBloqueo({
  idu_conciliacion_bloqueo,
  idu_conciliacion,
  des_mensaje_bitacora,
  fec_bloqueo,
  opc_estatus,
  idu_estatus_conciliacion,
}) {
  const parametros = [
    idu_conciliacion_bloqueo,
    idu_conciliacion,
    des_mensaje_bitacora,
    fec_bloqueo,
    opc_estatus,
    idu_estatus_conciliacion,
  ];
  const query = 'SELECT fun_actualizarconciliacionbloqueo($1, $2, $3, $4, $5, $6) as respuesta';

  return db.query(query, parametros);
}

/**
 * Función que elimina un bloqueo.
 * @param {Number} iduBloqueo Identificador del bloqueo.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function eliminarConciliacionBloqueo({
  idu_conciliacion_bloqueo,
}) {
  const parametros = [idu_conciliacion_bloqueo];
  const query = 'SELECT fun_eliminarconciliacionbloqueo($1) as respuesta';

  return db.query(query, parametros);
}
/**
 * Función para obtener el bloqueo de una conciliación en una fecha determinada.
 * @param {String} iduConciliacion Identificador de la conciliación.
 * @param {String} fecha Fecha para buscar el bloqueo.
 * @returns {Promise<import('pg').QueryResult<{
 * idu_conciliacion_bloqueo: Number,
 * fec_bloqueo: String,
 * fec_registro: String,
 * idu_usuario_registro: Number
 * nom_empleado: String,
 * idu_estatus_conciliacion: Number,
 * des_estatus: String,
 * count: Number
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerBloqueosPorConciliacionFecha({
  iduConciliacion,
  fecha,
}) {
  const parametros = [iduConciliacion, fecha];
  const query = `
  SELECT
    idu_conciliacion_bloqueo,
    idu_conciliacion,
    des_mensaje_bitacora,
    fec_bloqueo,
    fec_registro,
    idu_usuario_registro,
    nom_empleado,
    idu_estatus_conciliacion,
    des_estatus,
    opc_estatus,
    count
  FROM
    fun_obtenerbloqueosporconciliacionfecha($1, $2)
  `;
  return db.query(query, parametros);
}
