import * as db from '@postgresql';

/**
 * Obtiene los registros pendientes, agrupados por día.
 * @param {Number} pagina Número de página.
 * @param {Number} numRegistros Total de registros a mostrar.
 * @param {Date} fecha Fecha pendiente.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerPendientes({
  pagina,
  numRegistros,
  fecha,
}) {
  const parametros = [pagina, numRegistros, fecha];
  const query = `
    SELECT
      fecha,
      clave,
      movimientos,
      importe,
      count
    FROM
      fun_obtenerregistrospendientes($1, $2, $3)
  `;
  return db.query(query, parametros);
}

/**
 * Obtiene todos los registros pendientes de agregarse a un archivo CBANCO.
 * @param {Number} pagina Número de página.
 * @param {Number} numRegistros Número total de registros.
 * @param {String} busqueda Texto para buscar.
 * @param {Date} fecha Fecha filtro.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerPendientesDetalles({
  pagina,
  numRegistros,
  busqueda,
  fecha,
}) {
  const parametros = [pagina, numRegistros, fecha, busqueda];
  const query = `
  SELECT
    fecha,
    fechavencimiento,
    rpu,
    caja,
    importe,
    fol_sucursal,
    clave,
    tienda,
    count,
    1 as estatus
  FROM
    fun_obtenerregistrospendientesdetalles($1, $2, $3, $4)
  `;
  return db.query(query, parametros);
}

/**
 * Obtiene todos los detalles de un sólo día.
 * @param {Number} pagina Número de página.
 * @param {Number} numRegistros Total de registros a mostrar.
 * @param {Date} fecha Fecha de los detalles.
 * @returns {Promise} Promesa con el resultado de la consulta
 */
export function obtenerPendientesDetallesFecha({
  pagina,
  numRegistros,
  fecha,
}) {
  const parametros = [pagina, numRegistros, fecha];
  const query = `
  SELECT
    fecha,
    rpu,
    caja,
    importe,
    fol_sucursal,
    clave,
    tienda,
    count,
    1 as estatus
  FROM
    fun_obtenerregistrospendientesdetallesfecha($1, $2, $3)
  `;
  return db.query(query, parametros);
}
