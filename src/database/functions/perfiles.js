import * as db from '@postgresql';

/**
 * Función para obtener los perfiles
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: Array<{
 *       idu_perfil: Number,
 *       nombre: String
 *       descripcion: String
 *       estatus: Boolean
 *       modulos: Array<{ idu_modulo: Number }>
 *    }>
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerPerfiles() {
  const parametros = ['', null, 0, true];
  const query = 'SELECT fun_obtenerperfilesmonitores($1, $2, $3, $4) as lista;';
  return db.query(query, parametros);
}

/**
 * Función para obtener la lista de perfiles
 * @param {String} [parametros.busqueda] Término a buscar
 * @param {Number} [parametros.registrosPorPagina] Registros por página
 * @param {Number} [parametros.pagina] Página
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: Array<{
 *       idu_perfil: Number,
 *       nombre: String
 *       descripcion: String
 *       estatus: Boolean
 *       modulos: Array<{ idu_modulo: Number }>
 *    }>
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerListaPerfiles({
  busqueda = '',
  registrosPorPagina = 10,
  pagina = 1,
}) {
  const parametros = [
    busqueda,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
  ];

  const query = 'SELECT fun_obtenerperfilesmonitores($1, $2, $3) AS lista;';

  return db.query(query, parametros);
}

/**
 * Función para obtener un perfil por Id
 *
 * @param {Number} iduPerfil Identificador del perfil a eliminar
 * @returns {Promise<import('pg').QueryResult<{
 *  perfil: {
 *    idu_perfil: Number,
 *    nombre: String
 *    descripcion: String
 *    estatus: Boolean
 *    modulos: Array<{ idu_modulo: Number }>
 *  }
 * }>>} Promesa
 * con el resultado de la consulta
 */
export function obtenerPerfil(iduPerfil) {
  const query = 'SELECT fun_obtenerperfilmonitores($1) AS perfil;';
  return db.query(query, [iduPerfil]);
}

/**
 * Función para insertar o actualizar un perfil
 *
 * @param {Object} parametros Parametros de la función
 * @param {String} parametros.nombre Nombre del perfil
 * @param {String} parametros.descripcion Descripción del perfil
 * @param {Boolean} parametros.estatus Estatus del perfil
 * @param {Array<Number>} [parametros.modulos] Identificadores de los módulos
 * @param {Array<Number>} [parametros.permisosEjecucion] Permisos de exjecución manual
 * @param {Number} [parametros.iduPerfil] Identificador del perfil a actualizar
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: { estatus: Boolean mensaje?: String } }>
 * } Promesa con el resultado de la consulta
 */
export function registrarActualizarPerfil({
  nombre = null,
  descripcion = null,
  estatus = null,
  modulos = null,
  permisosEjecucion = [],
  iduPerfil = null,
}) {
  const parametros = [
    nombre,
    descripcion,
    estatus,
    modulos ? `{${modulos.join(',')}}` : null,
    permisosEjecucion.length ? `{${permisosEjecucion.join(',')}}` : null,
    iduPerfil,
  ];

  const query = 'SELECT fun_upsertperfilmonitores($1, $2, $3, $4::INT[], $5::INT[], $6) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para eliminar un perfil
 *
 * @param {Number} iduPerfil Identificador del perfil a eliminar
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function eliminarPerfil(iduPerfil) {
  const query = 'SELECT fun_eliminarperfilmonitores($1) as resultado';
  return db.query(query, [iduPerfil]);
}
