import * as db from '@postgresql';

/**
 * Función para obtener un correo de conciliacion
 * @param {number} iduConciliacion
 * @param {boolean} [esAlerta]
 * @returns {Promise<{
 *  idu_correo: number;
 *
 * }>}
 */
export function obtenerCorreoConciliacion(iduConciliacion, esAlerta = false) {
  const parametros = [iduConciliacion, esAlerta ? '1' : '0'];
  const query = 'SELECT fun_obtenercorreoconciliacion($1, $2) AS resultado';
  return db.query(query, parametros);
}

/**
 * Función para obtener las plantillas de correo por tipo
 * de conciliación
 * @param {{
 *  iduConciliacion: number;
 *  pagina: number;
 *  registrosPorPagina: number;
 *  busqueda: string;
 * }} opciones
 * @returns {Promise<{
 *  total: number;
 *  registros: {
 *    idu_destinatario_correo: number;
 *    des_destinatario: string;
 *    opc_estatus: boolean;
 *    tipo: string;
 *  }[]
 * }>}
 */
export function obtenerDestinatarios({
  iduConciliacion, pagina, registrosPorPagina, busqueda,
}) {
  const parametros = [
    iduConciliacion,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
    busqueda,
  ];

  const query = 'SELECT fun_obtenerdestinatarios($1, $2, $3, $4) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para obtener un destinatario por su idu
 * @param {number} idu
 * @returns {Promise<{
 *  idu_correo_destinatario: number;
 *  des_destinatario: string;
 *  nom_destinatario: string;
 *  opc_estatus: boolean;
 * }>}
 */
export function obtenerDestinatario(idu) {
  const parametros = [idu];
  const query = 'SELECT fun_obtenerdestinatario($1) as resultado';
  return db.query(query, parametros);
}

/**
 * Función que registra o actualiza un destinatario
 * @param {{
 *  idu_correo_destinatario: number,
 *  des_destinatario: string,
 *  nom_destinatario: string,
 *  idu_conciliacion: number,
 *  opc_estatus: boolean,
 *  tipo: 'Soporte' | 'Usuario',
 * }} destinatario
 * @returns {Promise<number | null>}
 */
export function registrarActualizarDestinatario({
  idu_correo_destinatario,
  des_destinatario,
  nom_destinatario,
  idu_conciliacion,
  opc_estatus,
  tipo,
}) {
  const parametros = [
    idu_correo_destinatario,
    des_destinatario,
    nom_destinatario,
    idu_conciliacion,
    opc_estatus,
    tipo,
  ];

  const query = 'SELECT fun_upsertcorreodestinatario($1, $2, $3, $4, $5, $6) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para eliminar un destinatario
 * de correos de conciliación
 * @param {number} idu
 * @returns {Promise<boolean>}
 */
export function eliminarDestinatario(idu) {
  const parametros = [idu];
  const query = 'SELECT fun_eliminardestinatariocorreo($1) as resultado';
  return db.query(query, parametros);
}

/**
 * Función que actualiza la plantilla de correo de una
 * conciliacion
 * @param {{
 *  idu_correo: number
 *  des_asunto: string
 *  des_cuerpo: string
 * }} params
 */
export function actualizarPlantillaCorreo({
  idu_correo,
  des_asunto,
  des_cuerpo,
}) {
  const parametros = [
    idu_correo,
    des_asunto,
    des_cuerpo,
  ];
  const query = 'SELECT fun_updateplantillacorreoconciliacion($1, $2, $3) as resultado';
  return db.query(query, parametros);
}
