import * as db from '@postgresql';

/**
 * Función para obtener la bitacora de las conciliaciones
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} [parametros.iduConciliacion] Identificador de la conciliación
 * @param {String} [parametros.fecha] Fecha de la conciliación
 * @param {Number} [parametros.iduEstatusConciliacion] Identificador del estatus de la conciliación
 * @param {Number} [parametros.busqueda] Término de búsqueda de la conciliación
 * @param {Number} [parametros.tipoServicio] Tipo de servicio a consultar
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  idu_conciliacion: Number
 *  nombre_conciliacion: String
 *  idu_bitacora_conciliacion: Number
 *  descripcion: String
 *  completados: Number
 *  pendientes: Number
 *  tipo_ejecucion: String
 *  usuario: String
 *  fin_inicio: String
 *  fecha_fin: String
 *  estatus: String
 *  opc_correo_enviado: Boolean
 * }>>} Promesa con el resultado de la consulta
 *  usuario: String
 */
export function obtenerBitacoraConciliaciones({
  iduConciliacion = null,
  fecha = null,
  iduEstatusConciliacion = null,
  busqueda = '',
  tipoServicio = '',
}) {
  const parametros = [iduConciliacion, fecha, iduEstatusConciliacion, busqueda, tipoServicio];

  const query = `
    SELECT
      idu_conciliacion,
      nombre_conciliacion,
      idu_bitacora_conciliacion,
      descripcion,
      completados,
      pendientes,
      tipo_ejecucion,
      usuario,
      fecha_inicio::TEXT,
      fecha_fin::TEXT,
      estatus,
      opc_correo_enviado
    FROM fun_obtenerbitacorasconciliaciones($1, $2, $3, $4, $5)
  `.replace(/\s+/g, ' ').trim();

  return db.query(query, parametros);
}

/**
 * Función para obtener los detalles de las bitacoras de las conciliaciones
 *
 * @param {Object}  parametros Parametros de la función
 * @param {Number}  parametros.iduBitacoraConciliacion Identificador de la bitácora de conciliación
 * @param {Number}  parametros.iduEstatusConciliacion Identificador del estatus de la conciliación
 * @param {Boolean} [parametros.busqueda] Término de búsqueda
 * @param {Number}  [parametros.registrosPorPagina] Registros por página
 * @param {Number}  [parametros.pagina] Página
 * @param {Boolean} [parametros.obtenerTodos] Indica si se deben obtener todos los registros
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: {
 *    total: Number
 *    registros: {
 *      fecha: String,
 *      conciliacion: String,
 *      descripcion: String,
 *      estatus: String
 *    }[]
 *  }
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerDetallesBitacorasConciliaciones({
  iduBitacoraConciliacion,
  iduEstatusConciliacion,
  busqueda,
  registrosPorPagina = 10,
  pagina = 1,
  obtenerTodos = false,
}) {
  const parametros = [iduBitacoraConciliacion];

  if (obtenerTodos) {
    parametros.push(...[null, null, null, null]);
  } else {
    parametros.push(...[
      iduEstatusConciliacion ? parseInt(iduEstatusConciliacion, 10) : null,
      busqueda,
      Number(registrosPorPagina),
      Number(pagina - 1 || 0) * Number(registrosPorPagina),
    ]);
  }

  const query = 'SELECT fun_obtenerdetallesbitacorasconciliaciones($1, $2, $3, $4, $5) AS resultado;';

  return db.query(query, parametros);
}

/**
 * Función para obtener la ruta del archivo de salida de una bitacora de conciliación
 * @param {Number} iduBitacoraConciliacion Identificador de la bitacora de conciliación
 * @returns {Promise<import('pg').QueryResult<{ ruta: String }>>} Promesa con el resultado
 * de la consulta
 */
export function obtenerRutaArchivoSalida(iduBitacoraConciliacion) {
  const query = 'SELECT fun_obtenerrutaarchivosalidadebitacora($1) as ruta';
  const parametros = [iduBitacoraConciliacion];

  return db.query(query, parametros);
}

/**
 * Función para obtener el estado de ejecución de una bitacora de conciliación
 * @param {Number} iduBitacoraConciliacion Identificador de la bitacora de conciliación
 * @returns {Promise<import('pg').QueryResult<{
 *  orden: Number,
 *  etapa: String,
 *  icono: String,
 *  estatus: String
 * }>>} Promesa con el resultado
 * de la consulta
 */
export function obtenerEstadoEjecucion(iduBitacoraConciliacion) {
  const query = 'SELECT orden, etapa, icono, estatus FROM fun_obtenerestadoconciliacion($1)';
  const parametros = [iduBitacoraConciliacion];

  return db.query(query, parametros);
}
