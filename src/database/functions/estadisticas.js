import * as db from '@postgresql';

/**
 * @typedef DetalleCorresponsales
 * @property {number} tipo_transaccion
 * @property {number} folio_bancoppel
 * @property {number} importe
 * @property {string} tienda
 * @property {string} estatus
 */

/**
 * @typedef DetalleAfore
 * @property {number} idu_conci_afore
 * @property {string} fecha_alta
 * @property {string} nss
 * @property {string} curp
 * @property {string} nom_trabajador
 * @property {string} nom_apellido_paterno
 * @property {string} nom_apellido_materno
 * @property {number} num_importe_neto_pagar
 * @property {number} num_folio
 * @property {number} num_tienda_solicitud
 * @property {string} fecha_captura
 * @property {string} fecha_pago
 * @property {string} hora_pago
 * @property {number} num_tienda_pago
 * @property {string} num_indentificacion
 * @property {string} opc_huella_gerente
 * @property {number} num_empleado_autoriza
 * @property {number} des_referencia_coppel
 * @property {string} num_caja
 * @property {string} estatus
 */

/**
 * @typedef DetalleABCCapital
 * @property {number} num_consecutivo
 * @property {string} referencia
 * @property {string} fecha_pago
 * @property {number} monto_pagado
 * @property {number} num_tienda
 * @property {string} area
 * @property {number} num_caja
 * @property {number} num_secuencia
 * @property {number} num_recibo
 * @property {string} empresa
 * @property {string} portafolio
 * @property {string} estatus
 */

/**
 * @typedef EstadisticaGeneralCorresponsales
 * @property {number} depositos_retiros_tarjetas_debito
 * @property {number} depositos_retiros_tarjetas_credito
 * @property {number} movimientos_transferencias_bancoppel
 * @property {number} diferencias
 */

/**
 * @typedef EstadisticaGeneralAfore
 * @property {number} registros_iniciales
 * @property {number} registros_conciliados
 * @property {number} registros_coppel_no_afore
 * @property {number} registros_afore_no_coppel
 */

/**
 * @typedef EstadisticaGeneralABCCapital
 * @property {number} registros_iniciales
 * @property {number} registros_conciliados
 * @property {number} registros_coppel_no_cyc
 * @property {number} registros_cyc_no_coppel
 */

/**
 * Función para obtener las estadísticas de las conciliaciones
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} parametros.iduConciliacion Identificador de la conciliación
 * @param {String} parametros.fecha Fecha de la conciliación
 * @param {Number} [parametros.estatus] Estatus de la conciliación
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  idu_bitacora_conciliacion?: Number,
 *  archivo_origen: String,
 *  descripcion: String,
 *  registros_origen: Number,
 *  incidencias: Number,
 *  estatus: Number
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerEstadisticasConciliaciones({ iduConciliacion, fecha, estatus = null }) {
  const parametros = [iduConciliacion, fecha, estatus];
  const query = 'SELECT fun_obtenerestadisticasconciliaciones($1, $2::DATE, $3) as resultado;';
  return db.query(query, parametros);
}

/**
 * Función para obtener las estadísticas generales de una conciliaciones
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} parametros.iduConciliacion Identificador de la conciliación
 * @param {String} parametros.fecha Fecha de la conciliación
 *
* @returns {Promise<import('pg').QueryResult<{
 *  resultado: EstadisticaGeneralCorresponsales | EstadisticaGeneralAfore | EstadisticaGeneralABCCapital
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerEstadisticasGeneralesConciliaciones({ iduConciliacion, fecha }) {
  const parametros = [iduConciliacion, fecha];
  const query = 'SELECT fun_obtenerestadisticasgeneralesconciliaciones($1, $2::DATE) as resultado;';
  return db.query(query, parametros);
}

/**
 * Función para obtener los detalles de las estadísticas de las conciliaciones
 *
 * @param {Object}  parametros Parametros de la función
 * @param {Number}  parametros.iduBitacoraConciliacion Identificador de la bitácora de conciliación
 * @param {String}  parametros.tipoArchivo Tipo de archivo de la conciliación
 * @param {String}  parametros.fecha Fecha de la conciliación
 * @param {Number}  parametros.estatus Estatus de la conciliación
 * @param {Boolean} [parametros.busqueda] Término de búsqueda
 * @param {Boolean} [parametros.tienda] Tienda
 * @param {Number}  [parametros.registrosPorPagina] Registros por página
 * @param {Number}  [parametros.pagina] Página
 * @param {Boolean} [parametros.obtenerTodos] Indica si se deben obtener todos los registros
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: {
 *    total: Number
 *    registros: Array<DetalleCorresponsales | DetalleAfore | DetalleABCCapital>
 *    archivo: String
 *  }
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerDetallesEstadisticasConciliaciones({
  iduBitacoraConciliacion,
  fecha,
  tipoArchivo = '',
  estatus,
  busqueda = '',
  tienda = '',
  registrosPorPagina = 10,
  pagina = 1,
  obtenerTodos = false,
}) {
  const parametros = [iduBitacoraConciliacion, fecha, tipoArchivo];

  if (obtenerTodos) {
    parametros.push(...[null, null, null, null, null]);
  } else {
    parametros.push(...[
      estatus ? parseInt(estatus, 10) : null,
      busqueda,
      tienda,
      Number(registrosPorPagina),
      Number(pagina - 1 || 0) * Number(registrosPorPagina),
    ]);
  }

  const query = 'SELECT fun_obtenerdetallesestadisticasconciliaciones($1, $2, $3, $4, $5, $6, $7, $8) AS resultado;';
  return db.query(query, parametros);
}

/**
 * Función para obtener las tiendas de las conciliaciones realizadas
 *
 * @param {Number} iduBitacoraConciliacion Identificador de la bitácora de conciliación
 *
 * @returns {Promise<import('pg').QueryResult<{ tienda: String }>>} Promesa
 * con el resultado de la consulta
 */
export function obtenerTiendasConciliaciones(iduBitacoraConciliacion) {
  const query = 'SELECT tienda FROM fun_obtenertiendasconciliaciones($1);';
  const params = [iduBitacoraConciliacion];

  return db.query(query, params);
}
