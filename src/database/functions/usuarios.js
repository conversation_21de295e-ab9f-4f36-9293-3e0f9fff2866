import * as db from '@postgresql';

/**
 * Función para obtener la lista de usuarios
 * @param {String} [parametros.busqueda] Término a buscar
 * @param {Number} [parametros.registrosPorPagina] Registros por página
 * @param {Number} [parametros.pagina] Página
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: {
*       idu_usuario: Number
 *      num_empleado: Number
 *      nom_empleado: String
 *      perfil: { idu_perfil: Number, nombre: String }
 *      nom_puesto: String
 *      num_telefono: String
 *      opc_estatus: Boolean
 *    }[]
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerListaUsuarios({
  busqueda = '',
  registrosPorPagina = 10,
  pagina = 1,
}) {
  const parametros = [
    busqueda,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
  ];

  const query = 'SELECT fun_obtenerusuariosmonitores($1, $2, $3) AS lista;';

  return db.query(query, parametros);
}

/**
 * Función para insertar o actualizar un usuario
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} parametros.iduPerfil Identificador del perfil
 * @param {Number} parametros.numEmpleado Número de empleado
 * @param {String} parametros.nomEmpleado Nombre del empleado
 * @param {String} parametros.nomPuesto Nombre del puesto
 * @param {String} parametros.numTelefono Número de teléfono
 * @param {Boolean} [parametros.estatus] Estatus del usuario
 * @param {Number} [parametros.iduUsuario] Identificador del usuario a actualizar
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: { estatus: Boolean mensaje?: String } }>
 * } Promesa con el resultado de la consulta
 */
export function registrarActualizarUsuario({
  iduPerfil = null,
  numEmpleado = null,
  nomEmpleado = null,
  nomPuesto = null,
  numTelefono = null,
  estatus = true,
  iduUsuario = null,
}) {
  const parametros = [
    iduPerfil,
    numEmpleado,
    nomEmpleado,
    nomPuesto,
    numTelefono,
    estatus,
    iduUsuario,
  ];

  const query = 'SELECT fun_upsertusuariomonitores($1, $2, $3, $4, $5, $6, $7) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para eliminar un usuario
 *
 * @param {Number} iduUsuario Identificador del usuario a eliminar
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function eliminarUsuario(iduUsuario) {
  const query = 'SELECT fun_eliminarusuariomonitores($1) as resultado';
  return db.query(query, [iduUsuario]);
}
