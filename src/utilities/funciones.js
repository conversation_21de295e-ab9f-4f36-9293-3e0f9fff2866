import { networkInterfaces } from 'os';
import { DateTime } from 'luxon';
import mime from 'mime-types';

/**
 * Función para obtener la dirección IP local
 * @returns {String} Dirección IP local
 */
export const obtenerIpLocal = () => {
  const nets = networkInterfaces();
  const results = Object.create(null);

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      const familyV4Value = typeof net.family === 'string' ? 'IPv4' : 4;
      if (net.family === familyV4Value && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  if (process.platform === 'darwin') {
    return results.en0[0];
  }
};

/**
 * Función para obtener el tipo de contenido de un archivo a partir de su extensión
 * @param {String} fileExtension Extensión del archivo
 * @returns {String} Tipo de contenido del archivo
 */
export const obtenerContentTypePorExtension = (fileExtension) => {
  const contentType = mime.contentType(fileExtension);
  return contentType || 'application/octet-stream'; // Tipo de contenido genérico si no se encuentra la extensión
};

/**
 * Función que actualiza las lineas de un archivo para ingresar los nuevos movimientos.
 * @param {*} contenido Contenido actual del archivo.
 * @param {*} nuevo Contenido nuevo que se agregará al archivo.
 * @returns Arreglo de lineas para escribir en el archivo.
 */
export const actualizarLineas = (contenido, nuevo) => {
  const {
    archivo: {
      fec_archivo, imp_archivo, num_movimientos,
      detalles: { detalle },
    },
  } = nuevo;

  const lineas = JSON.parse(JSON.stringify(contenido)).split(/\r?\n|\r|\n/g);
  let movimientos = lineas.filter((linea) => linea.startsWith('100000FA'));
  let cabecero = '000000732000000';
  cabecero += `${DateTime.fromISO(fec_archivo).toFormat('ddMMyyyy')}00`;
  cabecero += num_movimientos.toString().padStart(6, 0);
  cabecero += imp_archivo.toString().padStart(15, 0);
  cabecero += ''.padEnd(112, 0);

  const nuevos = detalle.map((det) => {
    let movimiento = '100000FA';
    movimiento += ''.padEnd(18, 0);
    movimiento += det.clv_rpu;
    movimiento += ' ';
    movimiento += ''.padEnd(7, 0);
    movimiento += DateTime.fromISO(det.fec_movimiento).toFormat('ddMMyyyy');
    movimiento += DateTime.fromISO(det.fec_vencimiento).toFormat('ddMMyyyy');
    movimiento += DateTime.fromISO(det.fec_movimiento).toFormat('ddMMyyyy');
    movimiento += ''.padEnd(4, 0);
    movimiento += det.imp_detalle.toString().padStart(10, 0);
    movimiento += ''.padEnd(25, 0);
    movimiento += det.imp_detalle.toString().padStart(10, 0);
    movimiento += ''.padEnd(39, 0);
    return movimiento;
  });

  let pie = '900000';
  pie += ''.padEnd(18, 0);
  pie += num_movimientos.toString().padStart(6, 0);
  pie += imp_archivo.toString().padStart(12, 0);
  pie += ''.padEnd(36, 0);
  pie += num_movimientos.toString().padStart(6, 0);
  pie += imp_archivo.toString().padStart(12, 0);
  pie += ''.padEnd(62, 0);

  movimientos = movimientos.concat(nuevos);
  return [cabecero, ...movimientos, pie, ''];
};
