/* eslint-disable key-spacing */
import { createLogger, format, transports } from 'winston';
import { DateTime } from 'luxon';
import { sync } from 'mkdirp';
import chalk from 'chalk';

const color = {
  info:    chalk.hex('#3259c2'), // Información general
  warn:    chalk.hex('#FFE600'), // Advertencias
  error:   chalk.hex('#F05C79'), // Errores críticos
  debug:   chalk.hex('#f294e2'), // Información de depuración
  verbose: chalk.hex('#95a5a6'), // Información detallada
  silly:   chalk.hex('#34495e'), // Información de menor prioridad
  server:  chalk.hex('#1abc9c'), // Información del servidor
};

const consoleFormat = format.printf(({ level, message, timestamp }) => {
  const mensaje = [];
  const fecha = DateTime.fromISO(timestamp).toFormat('dd-MM-yyyy HH:mm:ss');
  const levelColor = color[level] || color.info;

  mensaje.push(`[${chalk.gray(fecha)}]`);
  mensaje.push(`[${levelColor(level.toUpperCase())}]:`);
  mensaje.push(chalk.white(`${message}`));
  return mensaje.join(' ');
});

const fileFormat = format.printf(({
  level, message, timestamp, objeto,
}) => {
  const mensaje = [];
  const fecha = DateTime.fromISO(timestamp).toFormat('dd-MM-yyyy HH:mm:ss');

  mensaje.push(`[${fecha}]`);
  mensaje.push(`[${level.toUpperCase()}]:`);
  mensaje.push(`${message}`);
  if (objeto) mensaje.push(JSON.stringify(objeto));
  return mensaje.join(' ');
});

/**
 * Muestra un log en consola y lo guarda en un archivo.
 * @param {{
 *  isError: boolean
 *  title: string
 *  descripcion: string
 *  objeto: Error
 * }}
 */
export const log = ({
  isError, title, descripcion, objeto,
}) => {
  const fechaArchivo = DateTime.now().toISODate();
  const fechaCarpeta = DateTime.fromFormat(fechaArchivo, 'yyyy-MM-dd').setLocale('es').toFormat('MMMM-yyyy');
  const level = isError ? 'error' : 'info';
  const ruta = `./logs/${isError ? 'errors' : 'info'}/${fechaCarpeta}/`;
  const archivo = `${ruta}${fechaArchivo}.log`;
  const message = `${title} - ${descripcion ? `${descripcion}` : ''}`;

  sync(ruta);

  const fileLogger = createLogger({
    level,
    format: format.combine(
      format.timestamp(),
      format.json(),
    ),
    transports: [
      new transports.File({
        filename: archivo,
        level,
        format: format.combine(
          format.timestamp(),
          fileFormat,
        ),
      }),
    ],
  });

  fileLogger.log({ level, message, objeto });
};

const logger = createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  levels: {
    error: 0,
    warn: 1,
    server: 2,
    info: 3,
    http: 4,
    verbose: 5,
    mail: 6,
    silly: 7,
    debug: 8,
  },
  format: format.combine(
    format.timestamp(),
    consoleFormat,
  ),
  transports: [
    new transports.Console(),
  ],
});

if (process.env.NODE_ENV === 'development') {
  logger.debug = (...params) => {
    const fecha = DateTime.now().toFormat('dd-MM-yyyy HH:mm:ss');
    let mensaje = '';

    mensaje += `[${chalk.gray(fecha)}] `;
    mensaje += `[${color.debug('DEBUG')}]`;
    mensaje += ':';

    console.log(mensaje, ...params);
  };
}

global.logger = logger;
export default logger;
