/* eslint-disable max-len */
import passport from 'passport';
import passportAzureAd from 'passport-azure-ad';

import createHttpError from 'http-errors';
import { TOKEN_INVALIDO_NUMERO_EMPLEADO, TOKEN_INVALIDO_SCOPE } from '../constants/mensajes';

const configuracion = {
  credentials: {
    tenantID: process.env.AZURE_TENANT_ID,
    clientID: process.env.AZURE_CLIENT_ID,
    secretID: process.env.AZURE_SECRET_ID,
  },
  scope: [
    process.env.AZURE_SCOPE,
  ],
  metadata: {
    authority: 'login.microsoftonline.com',
    discovery: '.well-known/openid-configuration',
    version: 'v2.0',
  },
  settings: {
    validateIssuer: true,
    passReqToCallback: true,
    loggingLevel: 'error',
    loggingNoPII: true,
  },
  proxy: {
    host: process.env.AZURE_PROXY_HOST,
    port: process.env.AZURE_PROXY_PORT,
    protocol: process.env.AZURE_PROXY_PROTOCOL,
  },
};

/**
 * @type {import('passport-azure-ad').IBearerStrategyOptionWithRequest}
 */
const passporOptions = {
  identityMetadata: `https://${configuracion.metadata.authority}/${configuracion.credentials.tenantID}/${configuracion.metadata.version}/${configuracion.metadata.discovery}`,
  issuer: `https://${configuracion.metadata.authority}/${configuracion.credentials.tenantID}/${configuracion.metadata.version}`,
  clientID: configuracion.credentials.clientID,
  audience: configuracion.credentials.clientID,
  validateIssuer: configuracion.settings.validateIssuer,
  passReqToCallback: configuracion.settings.passReqToCallback,
  loggingLevel: configuracion.settings.loggingLevel,
  loggingNoPII: configuracion.settings.loggingNoPII,
  scope: configuracion.scope,
  proxy: configuracion.proxy,
};

const bearerStrategy = new passportAzureAd.BearerStrategy(passporOptions, async (req, token, done) => {
  /**
   * Si el número de empleado no se encuentra en el token, se considera inválido
   */
  if (!Object.prototype.hasOwnProperty.call(token, 'numeroEmpleado')) {
    return done(createHttpError(401, TOKEN_INVALIDO_NUMERO_EMPLEADO), null, null);
  }

  /**
   * Validar que posea el scope correcto, si el scope no es el correcto, se considera inválido
   */
  if (!Object.prototype.hasOwnProperty.call(token, 'scp') || token.scp !== process.env.AZURE_SCOPE) {
    return done(createHttpError(401, TOKEN_INVALIDO_SCOPE), null, null);
  }

  return done(null, token);
});

passport.use(bearerStrategy);

export default passport;
