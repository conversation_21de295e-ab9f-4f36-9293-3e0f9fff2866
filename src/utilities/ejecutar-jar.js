import { exec, spawn } from 'child_process';
import createHttpError from 'http-errors';

import {
  ARCHIVO_MANUAL_ERROR_JAR,
  ERROR_EJECUCION_JAR,
  JAR_NO_ENCONTRADO,
} from '../constants/mensajes';
import { TipoServicio } from '../constants';

import { log } from './logger';

const rutas = {
  [TipoServicio.CONCILIACION]: process.env.PATH_PROCESO_CONCILIACIONES_JAR,
  [TipoServicio.PAGO_SERVICIO]: process.env.PATH_PROCESO_PAGO_SERVICIOS_JAR,
  [TipoServicio.HOMOLOGADOS]: process.env.PATH_PROCESO_SERVICIOS_HOMLOGADOS_JAR,
};

/**
 * Ejecuta un archivo JAR para descargar un archivo del servidor
 * @param callback - Función que se ejecutará cuando el JAR ha sido ejecutado.
 */
export function bajarArchivoServidor({
  path_jdk, des_servidor, des_ruta, des_usuario, des_contrasena,
  nom_archivo, des_extension, ruta_jar, destino,
}, callback) {
  const parametros = [path_jdk, ruta_jar, des_servidor, des_ruta, des_usuario, des_contrasena,
    nom_archivo, des_extension, destino];
  let comando = '$0 -jar $1 $2 $3 $4 $5 $6 $7 $8';
  for (let i = 0; i < parametros.length; i += 1) {
    comando = comando.replace(`$${i}`, parametros[i]);
  }
  exec(comando, callback);
}

/**
 * Ejecuta un archivo JAR para subir el archivo al servidor
 */
export function subirArchivoServidor({
  path_jdk, des_servidor, des_ruta, des_usuario, des_contrasena,
  nom_archivo, nom_archivo_destino, des_extension, ruta_jar, destino,
}) {
  return new Promise((resolve, reject) => {
    const parametros = [path_jdk, ruta_jar, des_servidor, des_ruta, des_usuario, des_contrasena,
      nom_archivo, nom_archivo_destino, des_extension, destino];
    let comando = '$0 -jar $1 $2 $3 $4 $5 $6 $7 $8 $9';
    for (let i = 0; i < parametros.length; i += 1) {
      comando = comando.replace(`$${i}`, parametros[i]);
    }

    exec(comando, (error, stdout, stderr) => {
      if (process.env.NODE_ENV !== 'production') logger.server(`Ejecutando comando: ${comando}`);

      if (error) {
        log({
          isError: true,
          title: `Subida de archivos al servidor: ${ruta_jar}`,
          descripcion: `Comando: [${comando}]`,
          objeto: stderr,
        });
        reject(createHttpError(404, ARCHIVO_MANUAL_ERROR_JAR));
        return;
      }
      resolve(stdout);
    });
  });
}

/**
 * Método para ejecutar una conciliación manualmente
 *
 * @param {String} tipoServicio Servicio vinculado al proceso
 * @param {Array<any>} params Parámetros para el proceso
 *
 * @returns {Promise<String>} Promesa con la salida del proceso
 */
export function ejecutarConciliacion(servicio, params) {
  return new Promise((resolve, reject) => {
    const java = process.env.PATH_JDK;
    const procesoJar = rutas[servicio];

    if (!procesoJar) { reject(JAR_NO_ENCONTRADO); return; }

    // Sustituye cualquier carácter que no sea un número con una cadena vacía
    const paramsSanitizados = params.map((param) => {
      if (typeof param === 'string' && (/[^0-9]/g).test(param)) return 0;
      if (typeof param === 'number') return param;
      return 0;
    });

    // eslint-disable-next-line max-len
    if (process.env.NODE_ENV !== 'production') logger.server(`Ejecutando comando: ${java} -jar ${procesoJar} ${paramsSanitizados.join(' ')}`);
    const ejecucion = spawn(java, ['-jar', procesoJar, ...paramsSanitizados]);

    let stdout = '';
    let stderr = '';

    ejecucion.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    ejecucion.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    ejecucion.on('exit', (code) => {
      if (code === 0) {
        resolve(stdout);
      } else {
        log({
          isError: true,
          title: `[JAR]: ${procesoJar}`,
          descripcion: `Comando: [${java} -jar ${procesoJar} ${paramsSanitizados.join(' ')}]`,
          objeto: stderr,
        });
        reject(ERROR_EJECUCION_JAR);
      }
    });
  });
}
