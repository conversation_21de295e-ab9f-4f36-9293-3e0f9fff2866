/**
 * Exporta un archivo CSV con los datos de la tabla
 * @param {Array<{ [key: string]: any }>} registros Registros de la tabla
 * @returns {Buffer} Archivo CSV
 */
export function convertirCSV(registros) {
  const cabeceros = Object.keys(registros[0]).join(',');

  const contenido = registros.map((registro) => {
    const registrosFormateados = Object.values(registro)
      .map((valor) => {
        if (valor instanceof Date) {
          return valor.toISOString();
        }
        if (typeof valor === 'boolean') {
          return valor ? 'SI' : 'NO';
        }
        return valor;
      });
    const valores = registrosFormateados.join(',');
    return valores;
  });

  const contenidoCSV = [cabeceros, ...contenido].join('\n');

  return Buffer.from(contenidoCSV, 'utf8');
}
