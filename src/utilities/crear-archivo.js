import fs from 'fs';

/**
 * Función que crea un archivo en la ruta especificada, con el contenido enviado.
 * @param {String} nombreArchivo Nombre del archivo incluyendo la ruta del mismo
 * @param {String[]} datos Lineas con las que se llenará el archivo.
 */
export async function crearArchivo(nombreArchivo, datos) {
  try {
    const contenido = datos.join('\n');
    fs.writeFileSync(nombreArchivo, contenido);
  } catch (e) { /** Empty block */ }
}

/**
 * Lee el contenido de un archivo y lo regresa en una sola cadena de texto.
 * @param ruta - Cadena de texto que representa la ruta del archivo a leer.
 * @returns El contenido del archivo que se encuentra en la ruta especificada.
 */
export function leerArchivo(ruta) {
  let contenido = '';
  try {
    contenido = fs.readFileSync(ruta, 'utf8');
  } catch (e) { /** Empty block */ }
  return contenido;
}
