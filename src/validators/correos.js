import Joi from 'joi';

import { tipoDestinatarios } from '../constants';
import { opciones } from './utilidades-validacion';

export const esquemaPost = Joi.object({
  idu_conciliacion: Joi
    .number()
    .strict()
    .integer()
    .positive()
    .required(),
  nom_destinatario: Joi
    .string()
    .required(),
  des_destinatario: Joi
    .string()
    .email()
    .required(),
  tipo: Joi
    .allow(...Object.values(tipoDestinatarios))
    .required(),
  opc_estatus: Joi
    .boolean()
    .required(),
}).options(opciones);

export const plantillaEsquemaPut = Joi.object({
  des_asunto: Joi
    .string()
    .required(),
  des_cuerpo: Joi
    .string()
    .required(),
}).options(opciones);

export const destinatarioEsquemaPut = Joi.object({
  idu_conciliacion: Joi
    .number()
    .strict()
    .integer()
    .positive()
    .required(),
  nom_destinatario: Joi
    .string()
    .required(),
  des_destinatario: Joi
    .string()
    .email()
    .required(),
  tipo: Joi
    .allow(...Object.values(tipoDestinatarios))
    .required(),
  opc_estatus: Joi
    .boolean()
    .required(),
}).options(opciones);
