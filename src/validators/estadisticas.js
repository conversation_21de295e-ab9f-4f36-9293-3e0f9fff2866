import Joi from 'joi';

import { TipoServicio } from '../constants';
import {
  TIPO_CONCILIACION_INVALIDO,
  VALOR_BOOLEANO_INVALIDO,
} from '../constants/mensajes';

import { booleanNumberRegex } from '../utilities';
import {
  opciones,
  opcionesEnParams,
  validarServicioEspecifico,
} from './utilidades-validacion';

export const esquemaEstadisticas = {
  params: Joi.object({
    iduConciliacion: Joi
      .number()
      .integer()
      .positive()
      .custom(validarServicioEspecifico(TipoServicio.CONCILIACION))
      .required()
      .messages({
        'tipo_servicio.invalid': TIPO_CONCILIACION_INVALIDO,
      }),
  }).options(opciones),
  query: Joi.object({
    estatus: Joi
      .string()
      .pattern(booleanNumberRegex)
      .messages({
        'string.pattern.base': VALOR_BOOLEANO_INVALIDO,
      }),
    fecha: Joi
      .date()
      .iso(),
  }).options(opciones),
};

export const esquemaEstadisticasGenerales = {
  params: Joi.object({
    iduConciliacion: Joi
      .number()
      .integer()
      .positive()
      .custom(validarServicioEspecifico(TipoServicio.CONCILIACION))
      .required()
      .messages({
        'tipo_servicio.invalid': TIPO_CONCILIACION_INVALIDO,
      }),
  }).options(opciones),
  query: Joi.object({
    fecha: Joi
      .date()
      .iso(),
  }).options(opciones),
};

export const esquemaEstadisticaDetalles = {
  params: Joi.object({
    iduBitacoraConciliacion: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opcionesEnParams),
  query: Joi.object({
    tipoArchivo: Joi
      .string(),
    fecha: Joi
      .date()
      .iso(),
    busqueda: Joi
      .string()
      .allow(''),
    tienda: Joi
      .string()
      .allow(''),
    estatus: Joi
      .number()
      .integer()
      .positive(),
    registrosPorPagina: Joi
      .number()
      .integer()
      .positive(),
    pagina: Joi
      .number()
      .integer()
      .positive(),
    exportar: Joi
      .string()
      .pattern(booleanNumberRegex)
      .messages({
        'string.pattern.base': VALOR_BOOLEANO_INVALIDO,
      }),
  }).options(opciones),
};

export const esquemaTiendas = Joi.object({
  iduBitacoraConciliacion: Joi
    .number()
    .integer()
    .positive()
    .required(),
}).options(opciones);
