import Joi from 'joi';

import { opciones } from './utilidades-validacion';

export const esquemaPost = Joi.object({
  idu_perfil: Joi
    .number()
    .integer()
    .positive()
    .required(),
  num_empleado: Joi
    .number()
    .integer()
    .positive()
    .required(),
  nom_empleado: Joi
    .string()
    .required(),
  nom_puesto: Joi
    .string()
    .required(),
  num_telefono: Joi
    .string()
    .max(10)
    .required(),
  opc_estatus: Joi
    .boolean(),
}).options(opciones);

export const esquemaPut = {
  params: Joi.object({
    idu: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opciones),
  body: Joi.object({
    idu_perfil: Joi
      .number()
      .integer()
      .positive(),
    opc_estatus: Joi
      .boolean(),
  })
    .or(
      'idu_perfil',
      'opc_estatus',
    )
    .options(opciones),
};
