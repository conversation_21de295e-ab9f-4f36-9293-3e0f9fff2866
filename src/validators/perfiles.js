import Joi from 'joi';

import { opciones } from './utilidades-validacion';

export const esquemaPost = Joi.object({
  nom_perfil: Joi
    .string()
    .max(50)
    .required(),
  des_perfil: Joi
    .string()
    .required(),
  opc_estatus: Joi
    .boolean()
    .required(),
  modulos: Joi
    .array()
    .items(Joi.number().integer().positive().required())
    .required(),
  permisos_ejecucion: Joi
    .array()
    .items(Joi.number().integer().positive()),
}).options(opciones);

export const esquemaPut = {
  params: Joi.object({
    idu: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opciones),
  body: Joi.object({
    nom_perfil: Joi
      .string()
      .max(50),
    des_perfil: Joi
      .string(),
    opc_estatus: Joi
      .boolean(),
    modulos: Joi
      .array()
      .items(Joi.number().integer().positive().required()),
    permisos_ejecucion: Joi
      .array()
      .items(Joi.number().integer().positive()),
  })
    .or('nom_perfil', 'des_perfil', 'opc_estatus')
    .options(opciones),
};
