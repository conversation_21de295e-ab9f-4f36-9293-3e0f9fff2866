import Joi from 'joi';

import { opciones } from './utilidades-validacion';

export const esquemaListado = Joi.object({
  iduBitacoraConciliacion: Joi
    .number()
    .integer()
    .positive()
    .required(),
  iduEstatusConciliacion: Joi
    .number()
    .integer()
    .positive(),
  busqueda: Joi
    .string()
    .allow(''),
  registrosPorPagina: Joi
    .number()
    .integer()
    .positive(),
  pagina: Joi
    .number()
    .integer()
    .positive(),
}).options(opciones);

export const esquemaPost = Joi.object({
  idu_bitacora_conciliacion: Joi
    .number()
    .strict()
    .integer()
    .positive()
    .required(),
  idu_estatus_conciliacion: Joi
    .number()
    .strict()
    .integer()
    .positive()
    .required(),
  persona_inspeccion: Joi
    .string()
    .required(),
  observacion: Joi
    .string()
    .required(),
}).options(opciones);

export const esquemaPut = {
  params: Joi.object({
    iduBitacoraAccionCorreccion: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opciones),
  body: Joi.object({
    idu_estatus_conciliacion: Joi
      .number()
      .strict()
      .integer()
      .positive(),
    persona_inspeccion: Joi
      .string(),
    observacion: Joi
      .string(),
  })
    .or('idu_estatus_conciliacion', 'persona_inspeccion', 'observacion')
    .options(opciones),
};
