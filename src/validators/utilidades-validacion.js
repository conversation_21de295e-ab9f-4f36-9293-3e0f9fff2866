/* eslint-disable key-spacing */
import { CONCILIACIONES } from '../constants/conciliaciones';
import { DIF<PERSON>ENCIAS, EXITOSO } from '../constants/estatus';

import {
  CAMPOS_CARACTERES_MAXIMOS,
  CAMPOS_FALTANTES,
  CAMPO_ARREGLO,
  CAMPO_ARREGLO_VACIO,
  CAMPO_EMAIL_INVALIDO,
  CAMPO_FUERA_DE_DEFINICION,
  CAMPO_INVALIDO,
  CAMPO_NUMERICO,
  CAMPO_NUMERICO_POSITIVO,
  CAMPO_REQUERIDO,
  FORMATO_FECHA,
  PARAMETRO_INVALIDO,
  PARAMETRO_REQUERIDO,
} from '../constants/mensajes';

export const opciones = {
  messages: {
    'any.invalid'    : CAMPO_INVALIDO,
    'any.required'   : CAMPO_REQUERIDO,
    'number.base'    : CAMPO_NUMERICO,
    'number.integer' : CAMPO_INVALIDO,
    'number.positive': CAMPO_NUMERICO_POSITIVO,
    'object.unknown' : CAMPO_FUERA_DE_DEFINICION,
    'date.format'    : FORMATO_FECHA,
    'string.empty'   : CAMPO_REQUERIDO,
    'string.max'     : CAMPOS_CARACTERES_MAXIMOS,
    'string.email'   : CAMPO_EMAIL_INVALIDO,
    'boolean.base'   : CAMPO_INVALIDO,
    'object.missing' : CAMPOS_FALTANTES,
    'array.base'     : CAMPO_ARREGLO,
    'array.includesRequiredUnknowns': CAMPO_ARREGLO_VACIO,
  },
};

export const opcionesEnParams = {
  messages: {
    'any.invalid' : PARAMETRO_INVALIDO,
    'any.required': PARAMETRO_REQUERIDO,
    'number.base' : PARAMETRO_INVALIDO,
  },
};

/**
 * Función para validar el estatus de la conciliación.
 *
 * @param {string} estatus Estatus a validar
 * @param {import('joi').CustomHelpers} helpers Helpers de Joi
 *
 * @returns {import('joi').ErrorReport | string} Error o estatus validado
 */
export const validarEstatus = (estatus, helpers) => {
  const intEstatus = parseInt(estatus, 10);

  if (estatus) {
    if ((intEstatus !== EXITOSO && intEstatus !== DIFERENCIAS)) {
      return helpers.error('any.invalid', { v: estatus });
    }
  }

  return estatus;
};

/**
 * Función para validar el tipo de servicio.
 *
 * @param {String} tipoServicio Nombre del tipo de servicio
 * @param {import('joi').CustomHelpers} helpers Helpers de Joi
 *
 * @returns {import('joi').ErrorReport | string} Error o tipo de servicio validado
 */
export const validarTipoServicio = (tipoServicio, helpers) => {
  if (!tipoServicio) return tipoServicio;

  const esValido = CONCILIACIONES.find(
    ({ tipo }) => tipo === tipoServicio,
  );

  if (!esValido) {
    return helpers.error('tipo_servicio.invalid', { v: tipoServicio });
  }

  return tipoServicio;
};

/**
 * Valida si un servicio específico corresponde a una conciliación con un ID específico.
 *
 * @param {string} tipoServicio - El tipo de servicio que se desea validar.
 * @returns {function} - Una función que toma dos argumentos: el ID de la conciliación y un objeto helpers.
 *
 * @param {string} iduConciliacion - El ID de la conciliación que se desea validar.
 * @param {object} helpers - Un objeto que proporciona funciones de ayuda para manejar errores.
 * @returns {import('joi').ErrorReport | string} Error o tipo de servicio validado - El ID de la conciliación
 * si es válida; de lo contrario, se retorna un error con un mensaje personalizado.
 */
export const validarServicioEspecifico = (tipoServicio) => (iduConciliacion, helpers) => {
  const conciliacion = CONCILIACIONES.find(({ id }) => id === iduConciliacion);

  if (!conciliacion || conciliacion.tipo !== tipoServicio) {
    return helpers.error('tipo_servicio.invalid', { v: iduConciliacion });
  }

  return iduConciliacion;
};
