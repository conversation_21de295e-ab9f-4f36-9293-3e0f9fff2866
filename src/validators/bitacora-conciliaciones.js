import Joi from 'joi';

import { SERVICIO_NO_DISPONIBLE, VALOR_BOOLEANO_INVALIDO } from '../constants/mensajes';
import { opciones, opcionesEnParams, validarTipoServicio } from './utilidades-validacion';
import { booleanNumberRegex } from '../utilities';

export const esquemaBitacora = Joi.object({
  iduConciliacion: Joi
    .number()
    .integer()
    .positive(),
  busqueda: Joi
    .string()
    .allow(''),
  tipoServicio: Joi
    .string()
    .allow('')
    .custom(validarTipoServicio)
    .messages({
      'tipo_servicio.invalid': SERVICIO_NO_DISPONIBLE,
    }),
  iduEstatusConciliacion: Joi
    .number()
    .integer()
    .positive(),
  fecha: Joi
    .date()
    .iso(),
}).options(opciones);

export const esquemaBitacoraDetalles = {
  params: Joi.object({
    iduBitacoraConciliacion: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opcionesEnParams),
  query: Joi.object({
    iduEstatusConciliacion: Joi
      .number()
      .integer()
      .positive(),
    busqueda: Joi
      .string()
      .allow(''),
    registrosPorPagina: Joi
      .number()
      .integer()
      .positive(),
    pagina: Joi
      .number()
      .integer()
      .positive(),
    exportar: Joi
      .string()
      .pattern(booleanNumberRegex)
      .messages({
        'string.pattern.base': VALOR_BOOLEANO_INVALIDO,
      }),
  }).options(opciones),
};

export const esquemaBitacoraArchivoSalida = Joi.object({
  iduBitacoraConciliacion: Joi
    .number()
    .integer()
    .positive()
    .required(),
}).options(opciones);

export const esquemaBitacoraEjecucion = Joi.object({
  iduBitacoraConciliacion: Joi
    .number()
    .integer()
    .positive()
    .required(),
}).options(opciones);
