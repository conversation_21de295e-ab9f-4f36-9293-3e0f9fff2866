import app from 'express';
import createHttpError from 'http-errors';

import { registrarLogSeguridad } from '@functions/logs';
import { EVENTOS_SEGURIDAD } from '../constants';
import { ERROR_AUTENTICACION } from '../constants/mensajes';

const router = app.Router();

/**
 * Método para obtener los datos del empleado autenticado
 */
router.get('/me', async (req, res, next) => {
  try {
    const { usuarioIdp } = req.session || {};
    const method = req.headers.idp;

    if (!usuarioIdp) {
      await registrarLogSeguridad({
        estatus: false,
        ip: req.ip,
        transaccion_realizada: EVENTOS_SEGURIDAD.AccesoNoAutorizado,
      });
      throw createHttpError(401, ERROR_AUTENTICACION);
    }

    return res
      .status(200)
      .json({
        usuarioIdp,
        idP: method,
        ...req.session.usuarioMonitor,
      });
  } catch (error) {
    next(error);
  }
});

export default router;
