import createHttpError from 'http-errors';
import { Router } from 'express';

import { esquemaListado } from '@validators/common';
import { esquemaPost, destinatarioEsquemaPut, plantillaEsquemaPut } from '@validators/correos';
import {
  actualizarPlantillaCorreo,
  eliminarDestinatario,
  obtenerCorreoConciliacion,
  obtenerDestinatario,
  obtenerDestinatarios,
  registrarActualizarDestinatario,
} from '@functions/correos';
import {
  loguearTransaccion,
  validarBody, validarCampos, validarQuery, validarServicioEspecifico,
} from '../middlewares';
import {
  ERROR_REGISTRO_DESTINATARIO,
  ERROR_ELIMINAR_DESTINATARIO,
  ERROR_REGISTRO_PLANTILLA,
} from '../constants/mensajes';

const router = Router();

/**
 * Método para obteber la plantilla de correo de una
 * conciliación
 */
router.get('/conciliaciones/:idu', validarServicioEspecifico(), async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await obtenerCorreoConciliacion(idu);
    return res.status(200).json(resultado);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener los destinatarios de correo de una
 * conciliación
 */
router.get(
  '/destinatarios/conciliaciones/:idu',
  validarServicioEspecifico(),
  validarQuery(esquemaListado),
  async (req, res, next) => {
    try {
      const { idu: iduConciliacion } = req.params;
      const {
        busqueda = '',
        registrosPorPagina = 10,
        pagina = 1,
      } = req.query;

      const { rows: [{ resultado }] } = await obtenerDestinatarios({
        iduConciliacion,
        registrosPorPagina,
        pagina,
        busqueda,
      });

      return res.status(200).json(resultado);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * Método para obtener a un destinatario de correo por su
 * idu
 */
router.get('/destinatarios/:idu', async (req, res, next) => {
  try {
    const { idu } = req.params;

    const { rows: [{ resultado }] } = await obtenerDestinatario(idu);

    return res.status(200).json(resultado);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para registrar un nuevo destinatario de correo
 */
router.post('/destinatarios', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const {
      nom_destinatario,
      des_destinatario,
      opc_estatus,
      tipo,
      idu_conciliacion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarDestinatario({
      nom_destinatario,
      des_destinatario,
      opc_estatus,
      tipo,
      idu_conciliacion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_DESTINATARIO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar una plantilla de correo
 * de conciliación
 */
router.put('/:idu', validarBody(plantillaEsquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu: idu_correo } = req.params;
    const {
      des_asunto,
      des_cuerpo,
    } = req.body;

    const { rows: [{ resultado }] } = await actualizarPlantillaCorreo({
      idu_correo,
      des_asunto,
      des_cuerpo,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_PLANTILLA);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar un destinatario de correo de
 * conciliación
 */
router.put('/destinatarios/:idu', validarCampos(destinatarioEsquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const {
      idu,
    } = req.params;

    const {
      nom_destinatario,
      des_destinatario,
      opc_estatus,
      tipo,
      idu_conciliacion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarDestinatario({
      idu_correo_destinatario: Number(idu),
      nom_destinatario,
      des_destinatario,
      opc_estatus,
      tipo,
      idu_conciliacion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_DESTINATARIO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para eliminar a un destinatario de correo
 * conciliación por su idu
 */
router.delete('/destinatarios/:idu', loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await eliminarDestinatario(Number(idu));

    if (!resultado) throw createHttpError(400, ERROR_ELIMINAR_DESTINATARIO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
