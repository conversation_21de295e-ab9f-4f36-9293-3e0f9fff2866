/* eslint-disable no-multi-spaces */
import app from 'express';
import createHttpError from 'http-errors';

import { obtenerBitacoraConciliaciones } from '@functions/bitacora-conciliaciones';
import { registrarLogSeguridad } from '@functions/logs';

import { EVENTOS_SEGURIDAD } from '../constants';
import { EXITOSO } from '../constants/cat-estatus-conciliaciones';
import {
  EMPLEADO_NO_AUTORIZADO,
  ERROR_AUTENTICACION,
  ERROR_PROCESO_PREVIO_EXITOSO,
  TIPO_CONCILIACION_INVALIDO,
} from '../constants/mensajes';
import { CONCILIACIONES } from '../constants/conciliaciones';

import { ejecutarConciliacion } from '../utilities/ejecutar-jar';

const router = app.Router();

/**
 * Método para ejecutar un proceso externo
 */
router.post('/ejecutar/:iduConciliacion', async (req, res, next) => {
  try {
    const { ip } = req;
    const { iduConciliacion } = req.params;

    const { usuarioIdp, usuarioMonitor } = req.session || {};
    if (!usuarioIdp || !usuarioMonitor) throw createHttpError(401, ERROR_AUTENTICACION);

    const { num_empleado, permisos_ejecucion } = usuarioMonitor;
    const proceso = CONCILIACIONES.find((conciliacion) => conciliacion.id === Number(iduConciliacion));
    if (!proceso) {
      await registrarLogSeguridad({
        ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.IntentoEjecucionManual(iduConciliacion, num_empleado),
      });
      throw createHttpError(404, TIPO_CONCILIACION_INVALIDO);
    }

    const tienePermiso = permisos_ejecucion.some((permiso) => permiso.idu_conciliacion === Number(iduConciliacion));
    if (!tienePermiso) {
      await registrarLogSeguridad({
        ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.IntentoEjecucionManual(iduConciliacion, num_empleado),
      });
      return res.status(403).json(EMPLEADO_NO_AUTORIZADO);
    }

    const { rows } = await obtenerBitacoraConciliaciones({ iduConciliacion });
    const existeConciliacionExitosa = rows.some(({ estatus }) => estatus === EXITOSO);
    if (existeConciliacionExitosa) return res.status(409).json(ERROR_PROCESO_PREVIO_EXITOSO);

    const parametros = [
      proceso.id,   // Id del servicio
      num_empleado, // Id del empleado
      1,            // Indica si es ejecucion manual
    ];

    try {
      await ejecutarConciliacion(proceso.tipo, parametros);
      await registrarLogSeguridad({
        ip,
        estatus: true,
        transaccion_realizada: EVENTOS_SEGURIDAD.EjecucionManual(iduConciliacion, num_empleado),
      });
    } catch (error) {
      await registrarLogSeguridad({
        ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.EjecucionManual(iduConciliacion, num_empleado),
      });
      return next(createHttpError(400, error));
    }

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
