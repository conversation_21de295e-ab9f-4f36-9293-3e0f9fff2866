import app from 'express';
import {
  obtenerPendientes,
  obtenerPendientesDetalles,
  obtenerPendientesDetallesFecha,
} from '@functions/tiecarmovhistorial';

const router = app.Router();

/**
 * Obtiene los registros pendientes, agrupados por día.
 */
router.get('/obtener-pendientes', async (req, res, next) => {
  const { pagina, numRegistros, fecha } = req.query;
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerPendientes({ pagina, numRegistros, fecha });
    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }

    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Obtiene todos los registros pendientes de agregarse a un archivo CBANCO.
 */
router.get('/obtener-pendientes-detalle', async (req, res, next) => {
  const {
    pagina, numRegistros, busqueda, fecha,
  } = req.query;
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerPendientesDetalles({
      pagina, numRegistros, fecha, busqueda,
    });

    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Obtiene todos los detalles de un sólo día.
 */
router.get('/obtener-pendientes-detalles-fecha', async (req, res, next) => {
  const { pagina, numRegistros, fecha } = req.query;
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerPendientesDetallesFecha({ pagina, numRegistros, fecha });
    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

export default router;
