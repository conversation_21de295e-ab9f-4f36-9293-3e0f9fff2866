import app from 'express';
import { DateTime } from 'luxon';

import {
  obtenerEstadisticasConciliaciones,
  obtenerDetallesEstadisticasConciliaciones,
  obtenerTiendasConciliaciones,
  obtenerEstadisticasGeneralesConciliaciones,
} from '@functions/estadisticas';

import {
  esquemaEstadisticas,
  esquemaEstadisticaDetalles,
  esquemaTiendas,
  esquemaEstadisticasGenerales,
} from '@validators/estadisticas';

import { convertirCSV } from '../utilities';
import { REGISTROS_VACIOS } from '../constants/mensajes';
import { validarCampos, validarParams } from '../middlewares/validar-datos-solicitud';

const router = app.Router();

/**
 * Método para obtener el detalle de las estadísticas de conciliaciones
 */
router.get('/detalle/:iduBitacoraConciliacion', validarCampos(esquemaEstadisticaDetalles), async (req, res, next) => {
  try {
    const { iduBitacoraConciliacion } = req.params;

    if (!/^\d+$/.test(iduBitacoraConciliacion)) {
      return res.status(400).json({ error: 'ID de bitácora inválido' });
    }

    const {
      tipoArchivo = '',
      fecha = DateTime.local().toISODate(),
      busqueda = '',
      tienda = '',
      estatus = null,
      registrosPorPagina = 10,
      pagina = 1,
    } = req.query;

    const exportar = !!Number(req.query.exportar);

    const { rows: [{ resultado }] } = await obtenerDetallesEstadisticasConciliaciones({
      iduBitacoraConciliacion,
      tipoArchivo,
      fecha,
      busqueda,
      tienda,
      estatus,
      pagina,
      registrosPorPagina,
      obtenerTodos: exportar,
    });

    const { total, registros, archivo } = resultado;

    if (exportar) {
      if (registros && !registros.length) return res.status(404).json(REGISTROS_VACIOS);

      const archivoCSV = convertirCSV(registros);
      const nombreArchivo = archivo;

      return res.status(200)
        .setHeader('Content-Type', 'text/csv')
        .setHeader('Content-Disposition', `attachment; filename=${nombreArchivo}`)
        .setHeader('Content-Length', Buffer.byteLength(archivoCSV))
        .setHeader('Content-Transfer-Encoding', 'binary')
        .setHeader('Expires', '0')
        .setHeader('X-Content-Type-Options', 'nosniff')
        .setHeader('X-Frame-Options', 'SAMEORIGIN')
        .setHeader('X-XSS-Protection', '1; mode=block')
        .setHeader('Content-Security-Policy', "default-src 'none'")
        .end(archivoCSV);
    }

    return res.status(200)
      .set({
        'Content-Type': 'application/json',
        'X-Content-Type-Options': 'nosniff',
        'X-XSS-Protection': '1; mode=block',
      })
      .json({ total, registros });
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el listado de las estadísticas de conciliaciones
 */
router.get('/:iduConciliacion', validarCampos(esquemaEstadisticas), async (req, res, next) => {
  try {
    const { iduConciliacion } = req.params;
    const {
      estatus,
      fecha = DateTime.local().toISODate(),
    } = req.query;

    const {
      rows: [{ resultado: estadisticas }],
    } = await obtenerEstadisticasConciliaciones({ iduConciliacion, fecha, estatus });

    return res.status(200).json(estadisticas || []);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el listado de las tiendas de las conciliaciones realizadas
 */
router.get('/:iduBitacoraConciliacion/tiendas', validarParams(esquemaTiendas), async (req, res, next) => {
  try {
    const { iduBitacoraConciliacion } = req.params;
    const { rows } = await obtenerTiendasConciliaciones(iduBitacoraConciliacion);

    const tiendas = rows.map(({ tienda }) => ({
      id: tienda,
      nombre: tienda,
    }));

    return res.status(200).json(tiendas);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener las estadísticas generales de una conciliaciones
 */
router.get('/:iduConciliacion/generales', validarCampos(esquemaEstadisticasGenerales), async (req, res, next) => {
  try {
    const { iduConciliacion } = req.params;
    const { fecha = DateTime.local().toISODate() } = req.query;

    const { rows: [{ resultado: estadisticas }] } = await obtenerEstadisticasGeneralesConciliaciones({
      iduConciliacion, fecha,
    });

    return res.status(200).json(estadisticas || {});
  } catch (error) {
    next(error);
  }
});

export default router;
