import app from 'express';
import createHttpError from 'http-errors';

import {
  obtenerBitacoraAccionCorrecciones,
  registrarActualizarAccionCorreccion,
} from '@functions/bitacora-accion-correcciones';

import {
  esquemaListado,
  esquemaPost,
  esquemaPut,
} from '@validators/bitacora-accion-correcciones';

import {
  loguearTransaccion, validarBody, validarCampos, validarQuery,
} from '../middlewares';
import { ERROR_ACTUALIZACION_ACCION_CORRECCION, ERROR_REGISTRO_ACCION_CORRECCION } from '../constants/mensajes';

const router = app.Router();

/**
 * Método para obtener el listado de la bitacora acción correcciones
 */
router.get('/', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      iduBitacoraConciliacion,
      iduEstatusConciliacion,
      busqueda = '',
      registrosPorPagina = 10,
      pagina = 1,
    } = req.query;

    const { rows: [{ resultado }] } = await obtenerBitacoraAccionCorrecciones({
      iduBitacoraConciliacion,
      iduEstatusConciliacion,
      busqueda,
      registrosPorPagina,
      pagina,
    });

    const { total, registros } = resultado;

    return res.status(200).json({ total, registros });
  } catch (error) {
    next(error);
  }
});

/**
 * Método para registrar una acción o corrección
 */
router.post('/', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const {
      idu_bitacora_conciliacion,
      idu_estatus_conciliacion,
      persona_inspeccion,
      observacion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarAccionCorreccion({
      iduBitacoraConciliacion: idu_bitacora_conciliacion,
      iduEstatusConciliacion: idu_estatus_conciliacion,
      personaInspeccion: persona_inspeccion,
      observacion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_ACCION_CORRECCION);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar una acción o corrección
 */
router.put('/:iduBitacoraAccionCorreccion', validarCampos(esquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { iduBitacoraAccionCorreccion } = req.params;
    const {
      idu_estatus_conciliacion,
      persona_inspeccion,
      observacion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarAccionCorreccion({
      iduBitacoraAccionCorreccion: Number(iduBitacoraAccionCorreccion),
      iduEstatusConciliacion: idu_estatus_conciliacion,
      personaInspeccion: persona_inspeccion,
      observacion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_ACTUALIZACION_ACCION_CORRECCION);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
