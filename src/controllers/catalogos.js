import app from 'express';

import { esquemaListado } from '@validators/common';
import {
  obtenerCatalogoEstatus,
  obtenerModulos,
  obtenerPalabrasClave,
  obtenerUsuarios,
} from '@functions/catalogos';
import { validarQuery } from '../middlewares';

const router = app.Router();

/**
 * Método para obtener el catálogo de estatus
 */
router.get('/estatus', async (req, res, next) => {
  try {
    const { rows } = await obtenerCatalogoEstatus();

    return res.status(200).json(rows);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el catálogo de módulos
 */
router.get('/modulos', async (req, res, next) => {
  try {
    const { rows: [{ modulos }] } = await obtenerModulos();
    return res.status(200).json(modulos);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el catálogo de usuarios
 */
router.get('/usuarios', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = 10,
      pagina = 1,
    } = req.query;

    const { output } = await obtenerUsuarios({ busqueda, registrosPorPagina, pagina });

    return res.status(200).json({
      total: output.total,
      registros: JSON.parse(output.registros) || [],
    });
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el catálogo de palabras clave
 */
router.get('/palabras-clave', async (req, res, next) => {
  try {
    const { rows: [{ resultado }] } = await obtenerPalabrasClave();
    return res.status(200).json(resultado);
  } catch (error) {
    next(error);
  }
});

export default router;
