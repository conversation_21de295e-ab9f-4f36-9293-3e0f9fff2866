import app from 'express';
import createHttpError from 'http-errors';

import {
  eliminarUsuario,
  obtenerListaUsuarios,
  registrarActualizarUsuario,
} from '@functions/usuarios';

import { esquemaPost, esquemaPut } from '@validators/usuarios';
import { esquemaListado, esquemaPorId } from '@validators/common';
import { registrarLogSeguridad } from '@functions/logs';

import { EVENTOS_SEGURIDAD } from '../constants';
import {
  loguearTransaccion,
  validarBody,
  validarCampos,
  validarParams,
  validarQuery,
} from '../middlewares';

import {
  ERROR_ACTUALIZACION_USUARIO,
  ERROR_ELIMINAR_USUARIO,
  ERROR_REGISTRO_USUARIO,
} from '../constants/mensajes';

const router = app.Router();

/**
 * Método para obtener los usuarios
 */
router.get('/lista', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = 10,
      pagina = 1,
    } = req.query;

    const { rows: [{ lista: { total, registros } }] } = await obtenerListaUsuarios({
      busqueda,
      registrosPorPagina,
      pagina,
    });

    return res.status(200).json({ total, registros: registros || [] });
  } catch (error) {
    next(error);
  }
});

/**
 * Método para crear un usuario
 */
router.post('/', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const { ip } = req;
    const { usuarioMonitor } = req.session;

    const {
      idu_perfil,
      num_empleado,
      nom_empleado,
      nom_puesto,
      num_telefono,
      opc_estatus = true,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarUsuario({
      iduPerfil: idu_perfil,
      numEmpleado: num_empleado,
      nomEmpleado: nom_empleado,
      nomPuesto: nom_puesto,
      numTelefono: num_telefono,
      estatus: opc_estatus,
    });

    if (!resultado.estatus) {
      await registrarLogSeguridad({
        ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioRegistrado(usuarioMonitor.num_empleado),
      });
      throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_USUARIO);
    }

    await registrarLogSeguridad({
      ip,
      estatus: true,
      transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioRegistrado(usuarioMonitor.num_empleado),
    });
    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar un usuario
 */
router.put('/:idu', validarCampos(esquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { ip } = req;
    const { idu } = req.params;
    const { idu_perfil, opc_estatus } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarUsuario({
      iduUsuario: Number(idu),
      iduPerfil: idu_perfil,
      estatus: opc_estatus,
    });

    if (!resultado.estatus) {
      await registrarLogSeguridad({
        ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioActualizado(idu, req.session.usuarioMonitor.num_empleado),
      });
      throw createHttpError(400, resultado.mensaje || ERROR_ACTUALIZACION_USUARIO);
    }

    await registrarLogSeguridad({
      ip,
      estatus: true,
      transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioActualizado(idu, req.session.usuarioMonitor.num_empleado),
    });

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para eliminar un usuario
 */
router.delete('/:idu', validarParams(esquemaPorId), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await eliminarUsuario(Number(idu));

    if (!resultado) {
      await registrarLogSeguridad({
        ip: req.ip,
        estatus: false,
        transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioEliminado(idu, req.session.usuarioMonitor.num_empleado),
      });
      throw createHttpError(400, ERROR_ELIMINAR_USUARIO);
    }

    await registrarLogSeguridad({
      ip: req.ip,
      estatus: true,
      transaccion_realizada: EVENTOS_SEGURIDAD.UsuarioEliminado(idu, req.session.usuarioMonitor.num_empleado),
    });

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
