import app from 'express';
import fs from 'fs/promises';
import { DateTime } from 'luxon';

import {
  obtenerBitacoraConciliaciones,
  obtenerDetallesBitacorasConciliaciones,
  obtenerEstadoEjecucion,
  obtenerRutaArchivoSalida,
} from '@functions/bitacora-conciliaciones';

import {
  esquemaBitacoraDetalles,
  esquemaBitacoraArchivoSalida,
  esquemaBitacora,
  esquemaBitacoraEjecucion,
} from '@validators/bitacora-conciliaciones';

import { validarCampos, validarParams, validarQuery } from '../middlewares/validar-datos-solicitud';
import {
  BITACORA_CONCILIACION_ARCHIVO_NO_ENCONTRADO,
  BITACORA_CONCILIACION_SIN_ARCHIVO_SALIDA,
} from '../constants/mensajes';
import { convertirCSV, obtenerContentTypePorExtension } from '../utilities';

const router = app.Router();

/**
 * Método para obtener el listado de la bitacora de conciliaciones
 */
router.get('/', validarQuery(esquemaBitacora), async (req, res, next) => {
  try {
    const {
      iduConciliacion = null,
      tipoServicio = '',
      busqueda,
      iduEstatusConciliacion,
      fecha = DateTime.local().toISODate(),
    } = req.query;

    const { rows } = await obtenerBitacoraConciliaciones({
      iduConciliacion, fecha, iduEstatusConciliacion, busqueda, tipoServicio,
    });

    return res.status(200).json(rows);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener los detalles de una bitacora de conciliaciones
 */
router.get(
  '/detalle/:iduBitacoraConciliacion',
  validarCampos(esquemaBitacoraDetalles),
  async (req, res, next) => {
    try {
      const { iduBitacoraConciliacion } = req.params;

      if (!/^\d+$/.test(iduBitacoraConciliacion)) {
        return res.status(400).json({ error: 'ID de bitácora inválido' });
      }

      const {
        iduEstatusConciliacion = null,
        busqueda = '',
        registrosPorPagina = 10,
        pagina = 1,
      } = req.query;

      const exportar = !!Number(req.query.exportar);

      const { rows: [{ resultado }] } = await obtenerDetallesBitacorasConciliaciones({
        iduBitacoraConciliacion: Number(iduBitacoraConciliacion),
        iduEstatusConciliacion,
        busqueda,
        registrosPorPagina,
        pagina,
        obtenerTodos: exportar,
      });

      const { total, registros } = resultado;

      if (exportar) {
        const archivo = convertirCSV(registros);

        return res.status(200)
          .setHeader('Content-Type', 'text/tab-separated-values')
          .setHeader('Content-Disposition', `attachment; filename=bitacora_${iduBitacoraConciliacion}.txt`)
          .setHeader('Content-Length', Buffer.byteLength(archivo))
          .setHeader('Content-Transfer-Encoding', 'binary')
          .setHeader('Expires', '0')
          .setHeader('X-Content-Type-Options', 'nosniff')
          .setHeader('X-Frame-Options', 'SAMEORIGIN')
          .setHeader('X-XSS-Protection', '1; mode=block')
          .setHeader('Content-Security-Policy', "default-src 'none'")
          .end(archivo);
      }

      return res.status(200)
        .set({
          'Content-Type': 'application/json',
          'X-Content-Type-Options': 'nosniff',
          'X-XSS-Protection': '1; mode=block',
        })
        .json({ total, registros });
    } catch (error) {
      next(error);
    }
  },
);

/**
 * Método para obtener el archivo de salida de una bitacora
 */
router.get(
  '/:iduBitacoraConciliacion/archivo-salida',
  validarParams(esquemaBitacoraArchivoSalida),
  async (req, res, next) => {
    try {
      const { iduBitacoraConciliacion } = req.params;

      const { rows: [{ ruta }] } = await obtenerRutaArchivoSalida(Number(iduBitacoraConciliacion));

      if (!ruta) return res.status(400).json(BITACORA_CONCILIACION_SIN_ARCHIVO_SALIDA);

      try {
        await fs.access(ruta);
      } catch (error) {
        return res.status(404).json(BITACORA_CONCILIACION_ARCHIVO_NO_ENCONTRADO);
      }

      const archivoSalida = await fs.readFile(ruta);
      const nombreArchivo = ruta.split('/').pop();
      const extension = nombreArchivo.split('.').pop();
      const contentType = obtenerContentTypePorExtension(extension);

      return res.status(200)
        .setHeader('Content-Type', contentType)
        .setHeader('Content-Disposition', `attachment; filename=${nombreArchivo}`)
        .setHeader('Content-Length', Buffer.byteLength(archivoSalida))
        .setHeader('Content-Transfer-Encoding', 'binary')
        .setHeader('Expires', '0')
        .end(archivoSalida);
    } catch (error) {
      next(error);
    }
  },
);

/**
 * Método para obtener el estado de ejecución de una bitacora
 */
router.get(
  '/:iduBitacoraConciliacion/estado-ejecucion',
  validarParams(esquemaBitacoraEjecucion),
  async (req, res, next) => {
    try {
      const { iduBitacoraConciliacion } = req.params;
      const { rows } = await obtenerEstadoEjecucion(Number(iduBitacoraConciliacion));

      return res.status(200).json(rows);
    } catch (error) {
      next(error);
    }
  },
);

export default router;
