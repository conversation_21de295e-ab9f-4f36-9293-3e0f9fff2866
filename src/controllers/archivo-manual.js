import app from 'express';
import { DateTime } from 'luxon';

import {
  actualizarArchivo,
  cargarArchivo,
  obtenerArchivoFecha,
  obtenerArchivosCargados,
  obtenerDetallesArchivo,
  obtenerRegistrosCfe,
} from '@functions/archivo-manual';

import {
  actualizarConciliacionBloqueo,
  obtenerBloqueosPorConciliacionFecha,
} from '@functions/conciliacion-bloqueos';

import {
  obtenerOrigenPorClave,
} from '@functions/conciliacion-origenes';

import { loguearTransaccion } from '../middlewares';
import {
  ClaveOrigen,
  ConciliacionesEnum,
  EstatusConciliacionEnum,
} from '../constants';

import {
  ARCHIVO_MANUAL_ERROR_JAR,
  ARCHIVO_MANUAL_SIN_RUTA,
  ARCHIVO_MANUAL_SIN_RUTA_BD,
  ARCHIVO_MANUAL_SIN_RUTA_DESTINO,
  ARCHIVO_MANUAL_SIN_RUTA_JAR,
  ARCHIVO_MANUAL_SIN_RUTA_ORIGEN,
  ERROR_JAR_SIN_ARCHIVO,
} from '../constants/mensajes';

import {
  actualizarLineas,
  bajarArchivoServidor,
  crearArchivo,
  leerArchivo,
  subirArchivoServidor,
} from '../utilities';

const router = app.Router();

/**
 * Método que actualiza los registros de un archivo.
 */
router.put('/actualizar-archivo', loguearTransaccion, async (req, res, next) => {
  let contenido = '';
  let mensaje = '';
  try {
    const params = { iduConciliacion: ConciliacionesEnum.CFE, fecha: DateTime.now() };
    const [
      { rows: origenCbanco },
      { rows: origenJar },
      { rows: origenArchivo },
      { rows: destinoCbanco },
      { rows: rutaSubidaCbanco },
      { rows: bloqueos },
    ] = await Promise.all([
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_ORIGEN),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_JAR),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_ARCHIVO),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_DESTINO),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_BD),
      obtenerBloqueosPorConciliacionFecha(params),
    ]);

    if (origenCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_ORIGEN;
    } else if (origenJar.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_JAR;
    } else if (origenArchivo.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA;
    } else if (destinoCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_DESTINO;
    } else if (rutaSubidaCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_BD;
    }

    if (!mensaje) {
      const rutaCBanco = origenCbanco[0];
      const { des_ruta: rutaJar, nom_archivo: nomJar } = origenJar[0];
      const { des_ruta: rutaArchivo } = origenArchivo[0];
      const { des_ruta: destino } = destinoCbanco[0];
      const rutaSubida = rutaSubidaCbanco[0];

      const fPegada = '@f-fin-pegada-alreves;';
      rutaCBanco.nom_archivo = rutaCBanco.nom_archivo
        .replace(fPegada, DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'));

      const rutaCompleta = rutaArchivo + rutaCBanco.nom_archivo + rutaCBanco.des_extension;
      contenido = leerArchivo(rutaCompleta);
      contenido = actualizarLineas(contenido, req.body);
      crearArchivo(rutaCompleta, contenido);

      rutaSubida.nom_archivo = rutaCBanco.nom_archivo
        .replace(fPegada, DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'));

      const parametros = {
        ...rutaSubida,
        path_jdk: process.env.PATH_JDK,
        ruta_jar: rutaJar + nomJar,
        destino,
        nom_archivo_destino: rutaSubida.nom_archivo,
      };

      await subirArchivoServidor(parametros);
      await actualizarArchivo(DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'));

      const bloqueo = bloqueos[0];
      if (bloqueo && bloqueo.idu_estatus_conciliacion === EstatusConciliacionEnum.Pendiente) {
        bloqueo.idu_estatus_conciliacion = EstatusConciliacionEnum.Exitoso;
        await actualizarConciliacionBloqueo(bloqueo);
      }
      return res.status(200).json(contenido);
    }

    return res.status(404).json(mensaje);
  } catch (error) {
    next(error);
  }
});

/**
 * Función que obtiene todos los registros de la tabla sicfemovimientos.
 */
router.get('/', async (req, res, next) => {
  const { pagina, numRegistros } = req.query;
  let respuesta = { rows: [], count: 0 };
  try {
    const { rows } = await obtenerRegistrosCfe({ pagina, numRegistros });
    if (rows.length > 0) {
      respuesta = {
        rows,
        count: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Método que obtiene un archivo por fecha.
 */
router.get('/obtener-archivo-fecha', async (req, res, next) => {
  const { fecha } = req.query;
  try {
    const { rows } = await obtenerArchivoFecha(fecha);
    return res.status(200).json(rows);
  } catch (error) {
    next(error);
  }
});

/**
 * Método que obtiene todos los archivos cargados.
 */
router.get('/obtener-archivos-cargados', async (req, res, next) => {
  const { pagina, numRegistros, fecha } = req.query;
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerArchivosCargados({ pagina, numRegistros, fecha });

    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Método que obtiene los detalles de un archivo.
 */
router.get('/obtener-detalles-archivo', async (req, res, next) => {
  const { pagina, numRegistros, archivoId } = req.query;
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerDetallesArchivo({ archivoId, pagina, numRegistros });
    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Método que carga el archivo del día anterior.
 */
router.post('/cargar-archivo-actual', loguearTransaccion, async (req, res, next) => {
  let mensaje = '';
  try {
    const [
      { rows: origenCbanco },
      { rows: origenJar },
      { rows: destinoCbanco },
      { rows: rutaSubidaCbanco },
    ] = await Promise.all([
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_ORIGEN),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_JAR),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_DESTINO),
      obtenerOrigenPorClave(ClaveOrigen.RUTA_CBANCO_BD),
    ]);

    if (origenCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_ORIGEN;
    } else if (origenJar.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_JAR;
    } else if (destinoCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_DESTINO;
    } else if (rutaSubidaCbanco.length === 0) {
      mensaje = ARCHIVO_MANUAL_SIN_RUTA_BD;
    }

    if (!mensaje) {
      const rutaCBanco = origenCbanco[0];
      const { des_ruta: rutaJar, nom_archivo: nomJar } = origenJar[0];
      const { des_ruta: destino } = destinoCbanco[0];
      const rutaSubida = rutaSubidaCbanco[0];

      const fPegada = '@f-fin-pegada-alreves;';
      rutaCBanco.nom_archivo = rutaCBanco.nom_archivo
        .replace(fPegada, DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'));

      let parametros = {
        comando: '$0 -jar $1 $2 $3 $4 $5 $6 $7 $8',
        path_jdk: process.env.PATH_JDK,
        ruta_jar: rutaJar + nomJar,
        destino,
        ...rutaCBanco,
      };
      // Ejecuta el jar para bajar el archivo al servidor del servicio.
      bajarArchivoServidor(parametros, async (error) => {
        if (error) {
          if (error.message.includes(ERROR_JAR_SIN_ARCHIVO)) mensaje = ERROR_JAR_SIN_ARCHIVO;
          else mensaje = ARCHIVO_MANUAL_ERROR_JAR;

          return res.status(404).json(mensaje);
        }

        rutaSubida.nom_archivo = rutaCBanco.nom_archivo
          .replace(fPegada, DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'));

        parametros = {
          ...rutaSubida,
          path_jdk: process.env.PATH_JDK,
          ruta_jar: rutaJar + nomJar,
          destino,
          nom_archivo_destino: rutaSubida.nom_archivo,
        };
        // Ejecuta el jar para subir el archivo al servidor de BD.
        await subirArchivoServidor(parametros);
        await cargarArchivo(DateTime.now().plus({ days: -1 }).toFormat('yyyyMMdd'), true);

        return res.status(204).json();
      });
    } else {
      return res.status(404).json(mensaje);
    }
  } catch (error) {
    next(error);
  }
});

export default router;
