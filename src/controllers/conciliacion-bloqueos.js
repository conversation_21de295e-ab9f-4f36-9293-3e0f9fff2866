import app from 'express';

import {
  actualizarConciliacionBloqueo,
  eliminarConciliacionBloqueo,
  guardarConcilicacionBloqueo,
  obtenerBloqueosPorConciliacion,
} from '@functions/conciliacion-bloqueos';
import { loguearTransaccion } from '../middlewares';

const router = app.Router();

/**
 * Metodo que crea un nuevo bloqueo para una conciliación.
 */
router.post('/', loguearTransaccion, async (req, res, next) => {
  try {
    await guardarConcilicacionBloqueo(req.body);
    return res.status(200).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Metodo que obtiene los bloqueos de una conciliación.
 */
router.get('/obtener-por-conciliacion', async (req, res, next) => {
  let respuesta = { registros: [], total: 0 };
  try {
    const { rows } = await obtenerBloqueosPorConciliacion(req.query);
    if (rows.length > 0) {
      respuesta = {
        registros: rows,
        total: rows[0].count,
      };
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Metodo que actualiza un bloqueo existente para una conciliación.
 */
router.put('/', loguearTransaccion, async (req, res, next) => {
  let respuesta = '0';
  try {
    const { rows } = await actualizarConciliacionBloqueo(req.body);
    if (rows?.length > 0) {
      respuesta = rows[0].respuesta.toString();
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

/**
 * Metodo que elimina un bloqueo existente para una conciliación.
 */
router.delete('/', loguearTransaccion, async (req, res, next) => {
  let respuesta = '0';
  try {
    const { rows } = await eliminarConciliacionBloqueo(req.query);
    if (rows?.length > 0) {
      respuesta = rows[0].respuesta.toString();
    }
    return res.status(200).json(respuesta);
  } catch (error) {
    next(error);
  }
});

export default router;
