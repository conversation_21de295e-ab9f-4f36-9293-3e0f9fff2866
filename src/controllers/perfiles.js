import app from 'express';
import createHttpError from 'http-errors';

import {
  eliminarPerfil, obtenerListaPerfiles, obtenerPerfil, obtenerPerfiles, registrarActualizarPerfil,
} from '@functions/perfiles';
import { esquemaPost, esquemaPut } from '@validators/perfiles';
import { esquemaListado, esquemaPorId } from '@validators/common';

import {
  loguearTransaccion,
  validarBody, validarCampos, validarParams, validarQuery,
} from '../middlewares';
import {
  ERROR_ACTUALIZACION_PERFIL,
  ERROR_ELIMINAR_PERFIL,
  ERROR_REGISTRO_PERFIL,
  PERFIL_NO_ENCONTRADO,
} from '../constants/mensajes';

const router = app.Router();

/**
 * Método para obtener los perfiles
 */
router.get('/', async (req, res, next) => {
  try {
    const { rows: [{ lista: { registros } }] } = await obtenerPerfiles();

    return res.status(200).json(registros);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener los perfiles con paginación
 */
router.get('/lista', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = 10,
      pagina = 1,
    } = req.query;

    const { rows: [{ lista }] } = await obtenerListaPerfiles({
      busqueda,
      registrosPorPagina,
      pagina,
    });

    return res.status(200).json(lista);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener un perfil por Id
 */
router.get('/:idu', validarParams(esquemaPorId), async (req, res, next) => {
  try {
    const { idu } = req.params;

    const { rows: [{ perfil }] } = await obtenerPerfil(Number(idu));
    if (!perfil) return res.status(404).json(PERFIL_NO_ENCONTRADO);

    return res.status(200).json(perfil);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para crear un perfil
 */
router.post('/', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const {
      nom_perfil,
      des_perfil,
      opc_estatus,
      modulos,
      permisos_ejecucion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarPerfil({
      nombre: nom_perfil,
      descripcion: des_perfil,
      estatus: opc_estatus,
      modulos,
      permisosEjecucion: permisos_ejecucion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO_PERFIL);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar un perfil
 */
router.put('/:idu', validarCampos(esquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;

    const {
      nom_perfil,
      des_perfil,
      opc_estatus,
      modulos,
      permisos_ejecucion,
    } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarPerfil({
      iduPerfil: Number(idu),
      nombre: nom_perfil,
      descripcion: des_perfil,
      estatus: opc_estatus,
      modulos,
      permisosEjecucion: permisos_ejecucion,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_ACTUALIZACION_PERFIL);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para eliminar un perfil
 */
router.delete('/:idu', validarParams(esquemaPorId), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await eliminarPerfil(Number(idu));

    if (!resultado) throw createHttpError(400, ERROR_ELIMINAR_PERFIL);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
