import createHttpError from 'http-errors';

import { obtenerEmpleadoPorId } from '@functions/sesiones';

import passport from '../utilities/azure';
import { meToken, verificarToken } from '../authentication/sso';
import {
  ERROR_AUTENTICACION, ERROR_VALIDAR_TOKEN, NO_IDP, NO_TOKEN_PROVIDED,
} from '../constants/mensajes';

/**
 * Middleware para validar la sesión del usuario
 * @param {import('express').Request} req Objeto de solicitud de Express
 * @param {import('express').Response} res Objeto de respuesta de Express
 * @param {import('express').NextFunction} next Función de siguiente middleware
 */
export async function validarSesion(req, res, next) {
  const token = req.headers.authorization;
  const method = req.headers.idp;
  req.session = {
    usuarioIdp: null,
    usuarioMonitor: null,
  };

  // Verificación de headers
  if (!token) {
    return res.status(401).json(NO_TOKEN_PROVIDED);
  }
  if (!method) {
    return res.status(401).json(NO_IDP);
  }

  // Verificación de token segun idp
  try {
    switch (method) {
      case 'SSO':
        await verificarToken(token);
        req.session.usuarioIdp = await meToken(token);
        req.session.usuarioMonitor = (await obtenerEmpleadoPorId(req.session.usuarioIdp.user)).rows[0].resultado;
        return next();
      case 'AAD':
        passport.authenticate('oauth-bearer', { session: false }, async (error, usuario) => {
          if (error) return next(error);
          if (usuario) {
            const numeroEmpleado = Number(usuario.numeroEmpleado);
            req.session.usuarioIdp = usuario;
            req.session.usuarioMonitor = (await obtenerEmpleadoPorId(numeroEmpleado)).rows[0].resultado;
            return next();
          }
          return next(createHttpError(401, ERROR_AUTENTICACION));
        })(req, res, next);
        break;
      default:
        return res.status(422).json(ERROR_VALIDAR_TOKEN);
    }
  } catch (error) {
    next(error);
  }
}
