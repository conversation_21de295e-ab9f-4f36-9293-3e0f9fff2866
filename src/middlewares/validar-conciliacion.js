import { CONCILIACIONES } from '../constants';
import { TIPO_CONCILIACION_INVALIDO } from '../constants/mensajes';

/**
 * Middleware de validación de tipo de conciliación
 * @param {number} [tipoServicio] Tipo de servicio
 * @returns {import('express').Handler} Middleware de validación de Joi
 */
export function validarServicioEspecifico(tipoServicio) {
  return function (req, res, next) {
    const { idu } = req.params;
    const conciliacion = CONCILIACIONES.find(({ id }) => id === +idu);

    if (!conciliacion || (tipoServicio && conciliacion.tipo !== tipoServicio)) {
      return res.status(400).json(TIPO_CONCILIACION_INVALIDO);
    }

    req.conciliacion = conciliacion;
    next();
  };
}
