import { registrarLogTransaccion } from '@functions/logs';

const obtenerRecurso = (url) => url.split('/').pop();

const TIPOS_TRANSACCION = {
  POST: 'Insercion',
  PUT: 'Actualizacion',
  DELETE: 'Eliminacion',
};

const obtenerTipoTransaccion = (method) => TIPOS_TRANSACCION[method] || '';

/**
 * @type {import('express').Handler}
 */
export const loguearTransaccion = (req, res, next) => {
  const { baseUrl, method, ip } = req;

  const recurso = obtenerRecurso(baseUrl);
  const tipoTransaccion = obtenerTipoTransaccion(method);

  res.on('finish', async () => {
    const { statusCode } = res;
    const { usuarioMonitor } = req.session;
    const { num_empleado } = usuarioMonitor;

    const estatus = statusCode >= 200 && statusCode < 300;

    registrarLogTransaccion({
      estatus,
      ip,
      num_empleado,
      recurso,
      rol: usuarioMonitor.nom_perfil,
      tipo_transaccion: tipoTransaccion,
    });
  });

  next();
};
