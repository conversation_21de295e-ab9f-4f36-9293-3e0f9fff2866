# RESTful API para CONCILIACIONES **COPPEL**

## Requisitos
* Node.js (https://nodejs.org/es/download/). Versión recomendada: 22.x
* Archivo ```.env```
  - **PRODUCTIVO**: Utiliza ```.env.production```
  - **DESARROLLO**: Utiliza ```.env.development```
* Permisos o accesos necesarios para que el proceso no tenga bloqueos

## Instalación
1. Ejecutar el comando ```npm install``` para reinstalar las paqueterias necesarias.
2. Configurar las variables de entorno del archivo ```.env.production```:
    - PORT => Puerto donde se ejecutará el servicio
    - NODE_ENV => Ambiente en el que se ejecutará el servicio

    - CORS_ORIGIN => Origen desde donde se pueden realizar peticiones

    - PATH_JDK => Ruta del JDK o Java a utilizar
    - PATH_PROCESO_CONCILIACIONES_JAR => Ruta de la aplicación dedicada para conciliaciones
    - PATH_PROCESO_PAGO_SERVICIOS_JAR => Ruta de la aplicación dedicada para pagos de servicio
    - PATH_PROCESO_SERVICIOS_HOMLOGADOS_JAR => Ruta de la aplicación dedicada para la conciliación de servicios homologados

    - LOG_QUERY => Bandera que indica si los query usados en la aplicación se guardaran en un log de información en el servidor

    - URL_OBTENER_TOKEN => Url del servicio para obtener un token de autorización
    - SITIBUNDUS => Sitibundus para obtener el token de autorización para  el servicio de credenciales
    - UNDIVAGO => undivago para obtener el token de autorización para  el servicio de credenciales
    - USER_TYPE => usertype para obtener el token de autorización para  el servicio de credenciales

    - URL_CREDENCIALES => URL del servicio de credenciales
    - URL_VALIDACION_TOKEN => URL del endpoint de validación del token para el servicio de credenciales
    - APP_NAME => Nombre de la aplicación para el servicio de credenciales
    - CATEGORIA_PERSONAL => Categoría para la conexión a la base de datos personal
    - CATEGORIA_INGRESOS => Categoría para la conexión a la base de datos ingresos

3. Ejectutar el comando ```npm run build``` para crear el build de producción.

## ¿Como ejecutarlo?
Para ejecutar el servicio, puedes utiizar los siguientes comandos:
```console
node build/bundle.js
```

o

```console
npm start
```

## ¿Utilizar algún gestor de procesos como **PM2**?
Si utilizas **PM2** para gestionar los procesos de aplicativos de Node, asegurate de crear y configurar tu archivo  ```ecosystem.config.js```

### Para inicializarlo
```console
pm2 start [ruta ecosystem.config.js]
```

### Para ver logs
```console
pm2 logs [id del proceso]
```

## Notas
  - Si deseas ver o dar seguimiento a los procesos o errores generados por el servicio, pudes entrar a la carpeta de **logs/**, en ella podras visualizar más a profundidad errores y procesos de cada día.